import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

/**
 * Contractor Company Management Functions
 * 
 * Specialized functions for managing contractor company profiles,
 * including updates and Brønnøysundregisteret data refresh.
 */

// Update contractor company information
export const updateContractorCompany = mutation({
  args: {
    clerkUserId: v.string(),
    // Company data fields
    name: v.optional(v.string()),
    contactPerson: v.optional(v.string()),
    phone: v.optional(v.string()),
    // email removed - now locked to Clerk authentication, not stored in database
    // Address fields
    streetAddress: v.optional(v.string()),
    postalCode: v.optional(v.string()),
    city: v.optional(v.string()),
    entrance: v.optional(v.string()),
    orgNumber: v.optional(v.string()),
    notes: v.optional(v.string()),
    // Brønnøysundregisteret data tracking
    brregFetchedAt: v.optional(v.number()),
    brregData: v.optional(v.object({
      name: v.optional(v.string()),
      orgNumber: v.optional(v.string()),
      organizationNumber: v.optional(v.string()),
      status: v.optional(v.string()),
      industryCode: v.optional(v.string()),
      industryDescription: v.optional(v.string()),
      organizationForm: v.optional(v.string()),
      organizationFormCode: v.optional(v.string()),
      naeringskode1: v.optional(v.string()),
      establishmentDate: v.optional(v.string()),
      numberOfEmployees: v.optional(v.number()),
      managingDirector: v.optional(v.union(
        v.string(), // Legacy format: just the name as string
        v.object({  // New detailed format from Brønnøysundregisteret
          birthDate: v.optional(v.string()),
          firstName: v.optional(v.string()),
          fullName: v.optional(v.string()),
          lastName: v.optional(v.string())
        })
      )),
      businessAddress: v.optional(v.object({
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      visitingAddress: v.optional(v.object({
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      registryContact: v.optional(v.object({
        phone: v.optional(v.string()),
        email: v.optional(v.string())
      }))
    })),
    useCustomAddress: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe contractor company update: No identity found for user", args.clerkUserId);
        throw new Error("Autentisering ikke fullført. Vennligst vent et øyeblikk og prøv igjen.");
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe contractor company update: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        throw new Error("Bruker-ID stemmer ikke overens. Vennligst logg inn på nytt.");
      }

    // Get user record to find contractor company
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user || !user.contractorCompanyId) {
      throw new Error("Ingen bedriftsprofil funnet for denne brukeren");
    }

    // Get contractor company
    const company = await ctx.db.get(user.contractorCompanyId);
    if (!company) {
      throw new Error("Bedriftsprofil ikke funnet");
    }

    // Verify this is actually a contractor company
    if (company.contractorUserId !== args.clerkUserId) {
      throw new Error("Du har ikke tilgang til å redigere denne bedriftsprofilen");
    }

    // Build update object with only provided fields
    const updates: any = {};
    
    // Basic company information
    if (args.name !== undefined) updates.name = args.name.trim();
    if (args.contactPerson !== undefined) updates.contactPerson = args.contactPerson?.trim() || undefined;
    if (args.phone !== undefined) updates.phone = args.phone?.trim() || undefined;
    // email processing removed - now locked to Clerk authentication
    
    // Address fields
    if (args.streetAddress !== undefined) updates.streetAddress = args.streetAddress?.trim() || undefined;
    if (args.postalCode !== undefined) updates.postalCode = args.postalCode?.trim() || undefined;
    if (args.city !== undefined) updates.city = args.city?.trim() || undefined;
    if (args.entrance !== undefined) updates.entrance = args.entrance?.trim() || undefined;
    
    // Organization data
    if (args.orgNumber !== undefined) {
      const cleanOrgNumber = args.orgNumber.replace(/\s/g, '');
      if (!/^\d{9}$/.test(cleanOrgNumber)) {
        throw new Error("Organisasjonsnummer må være 9 siffer");
      }
      updates.orgNumber = cleanOrgNumber;
    }
    
    if (args.notes !== undefined) updates.notes = args.notes?.trim() || undefined;
    
    // Brønnøysundregisteret data tracking
    if (args.brregFetchedAt !== undefined) updates.brregFetchedAt = args.brregFetchedAt;
    if (args.brregData !== undefined) updates.brregData = args.brregData;
    if (args.useCustomAddress !== undefined) updates.useCustomAddress = args.useCustomAddress;

    // Note: updatedAt field is not in the schema, so we don't add it
    // If we need this functionality, we should add it to the schema first

    // Update the company record
    await ctx.db.patch(user.contractorCompanyId, updates);

    // Return updated company data
    return await ctx.db.get(user.contractorCompanyId);

    } catch (error) {
      console.error("Safe contractor company update error:", error);
      const errorMessage = error instanceof Error ? error.message : "En feil oppstod ved oppdatering av bedriftsprofil";
      throw new Error(errorMessage);
    }
  },
});

// Refresh Brønnøysundregisteret data for contractor company
export const refreshBrregData = mutation({
  args: {
    clerkUserId: v.string(),
    brregData: v.object({
      name: v.optional(v.string()),
      orgNumber: v.optional(v.string()),
      organizationNumber: v.optional(v.string()),
      status: v.optional(v.string()),
      industryCode: v.optional(v.string()),
      industryDescription: v.optional(v.string()),
      organizationForm: v.optional(v.string()),
      organizationFormCode: v.optional(v.string()),
      naeringskode1: v.optional(v.string()),
      establishmentDate: v.optional(v.string()),
      numberOfEmployees: v.optional(v.number()),
      managingDirector: v.optional(v.union(
        v.string(),
        v.object({
          birthDate: v.optional(v.string()),
          firstName: v.optional(v.string()),
          fullName: v.optional(v.string()),
          lastName: v.optional(v.string())
        })
      )),
      businessAddress: v.optional(v.object({
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      visitingAddress: v.optional(v.object({
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      registryContact: v.optional(v.object({
        phone: v.optional(v.string()),
        email: v.optional(v.string())
      }))
    }),
    updateCompanyFields: v.optional(v.boolean()) // Whether to update company fields with registry data
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe Brreg refresh: No identity found for user", args.clerkUserId);
        throw new Error("Autentisering ikke fullført. Vennligst vent et øyeblikk og prøv igjen.");
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe Brreg refresh: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        throw new Error("Bruker-ID stemmer ikke overens. Vennligst logg inn på nytt.");
      }

    // Get user record to find contractor company
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user || !user.contractorCompanyId) {
      throw new Error("Ingen bedriftsprofil funnet for denne brukeren");
    }

    // Get contractor company
    const company = await ctx.db.get(user.contractorCompanyId);
    if (!company) {
      throw new Error("Bedriftsprofil ikke funnet");
    }

    // Verify this is actually a contractor company
    if (company.contractorUserId !== args.clerkUserId) {
      throw new Error("Du har ikke tilgang til å oppdatere denne bedriftsprofilen");
    }

    // Prepare updates
    const updates: any = {
      brregData: args.brregData,
      brregFetchedAt: Date.now(),
      updatedAt: Date.now()
    };

    // Optionally update company fields with registry data
    if (args.updateCompanyFields) {
      if (args.brregData.name) {
        updates.name = args.brregData.name;
      }

      // Update address from visiting address (preferred) or business address
      const addressSource = args.brregData.visitingAddress || args.brregData.businessAddress;
      if (addressSource) {
        if (addressSource.street) updates.streetAddress = addressSource.street;
        if (addressSource.postalCode) updates.postalCode = addressSource.postalCode;
        if (addressSource.city) updates.city = addressSource.city;
      }

      // Update contact information if available
      if (args.brregData.registryContact) {
        if (args.brregData.registryContact.phone) {
          // Format phone number for Norwegian display
          const phone = args.brregData.registryContact.phone.replace('+47', '').replace(/\s/g, '');
          if (phone.length === 8) {
            updates.phone = `+47 ${phone}`;
          }
        }
        // Email update removed - email is now locked to Clerk authentication
      }
    }

    // Update the company record
    await ctx.db.patch(user.contractorCompanyId, updates);

    // Return updated company data
    return await ctx.db.get(user.contractorCompanyId);

    } catch (error) {
      console.error("Safe Brreg refresh error:", error);
      const errorMessage = error instanceof Error ? error.message : "En feil oppstod ved oppdatering fra Brønnøysundregisteret";
      throw new Error(errorMessage);
    }
  },
});

// Get contractor company with enhanced data (SAFE VERSION)
export const getContractorCompanyWithDetails = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe contractor company check: No identity found for user", args.clerkUserId);
        return null; // Return null instead of throwing error
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe contractor company check: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return null; // Return null instead of throwing error
      }

      // Get user record to find contractor company
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (!user || !user.contractorCompanyId) {
        return null;
      }

      // Get contractor company
      const company = await ctx.db.get(user.contractorCompanyId);
      if (!company || company.contractorUserId !== args.clerkUserId) {
        return null;
      }

      return company;
    } catch (error) {
      console.error("Safe contractor company check error:", error);
      return null; // Return null instead of throwing error
    }
  },
});

// Validate organization number for contractor company updates
export const validateOrgNumberUpdate = query({
  args: {
    clerkUserId: v.string(),
    orgNumber: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    const cleanOrgNumber = args.orgNumber.replace(/\s/g, '');

    // Basic format validation
    if (!/^\d{9}$/.test(cleanOrgNumber)) {
      return {
        isValid: false,
        error: "Organisasjonsnummer må være 9 siffer",
      };
    }

    // Get current contractor company
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user || !user.contractorCompanyId) {
      return {
        isValid: false,
        error: "Ingen bedriftsprofil funnet",
      };
    }

    const currentCompany = await ctx.db.get(user.contractorCompanyId);
    if (!currentCompany) {
      return {
        isValid: false,
        error: "Bedriftsprofil ikke funnet",
      };
    }

    // Allow same org number if it's the current company's org number
    if (currentCompany.orgNumber === cleanOrgNumber) {
      return {
        isValid: true,
        error: null,
      };
    }

    // Check if another contractor company already uses this org number
    const existingCompany = await ctx.db
      .query("customers")
      .withIndex("by_org_number", (q) => q.eq("orgNumber", cleanOrgNumber))
      .filter((q) => q.neq(q.field("contractorUserId"), null))
      .first();

    if (existingCompany && existingCompany._id !== user.contractorCompanyId) {
      return {
        isValid: false,
        error: "Dette organisasjonsnummeret er allerede registrert av en annen leverandør",
      };
    }

    return {
      isValid: true,
      error: null,
    };
  },
});
