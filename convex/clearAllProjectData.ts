import { mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * DEVELOPMENT UTILITY: Comprehensive Database Reset
 *
 * This script safely deletes all user-generated data from the Convex database
 * while preserving the database schema structure and system integrity.
 *
 * ⚠️  WARNING: This is a destructive operation that cannot be undone!
 * ⚠️  Only use in development/testing environments!
 *
 * WHAT GETS DELETED (in correct dependency order):
 * - All typing indicators from the `typingIndicators` table
 * - All user notifications from the `notifications` table
 * - All link preview cache from the `linkPreviews` table
 * - All email tracking data from the `emailTracking` table
 * - All chat messages and reactions from the `messages` table
 * - All image likes and customer session data from the `imageLikes` table
 * - All user project notes from the `userProjectNotes` table
 * - All project assignments and team collaboration from the `projectAssignments` table
 * - All log entries and associated images from the `logEntries` table
 * - All projects (including archived ones) and job data from the `projects` table
 * - All customer records (both regular customers and contractor companies) from the `customers` table
 * - All user records from the `users` table (contractor onboarding data)
 * - All file storage references (images/attachments from Convex storage)
 *
 * WHAT IS PRESERVED:
 * - Database schema definitions and indexes
 * - System configuration and settings
 * - Clerk authentication system (external to Convex)
 *
 * SAFETY FEATURES:
 * - Dry-run option to preview deletions without executing
 * - Comprehensive logging of all operations
 * - Error handling with rollback information
 * - Confirmation code requirement
 * - Foreign key relationship respect
 *
 * USAGE:
 * From Convex Dashboard:
 * 1. Go to your project dashboard
 * 2. Navigate to "Functions" tab
 * 3. Find "clearAllProjectData" or "comprehensiveReset" mutation
 * 4. Execute with: { confirmationCode: "DELETE_ALL_PROJECT_DATA", dryRun: false }
 *
 * From CLI:
 * npx convex run clearAllProjectData:comprehensiveReset '{"confirmationCode": "DELETE_ALL_PROJECT_DATA", "dryRun": false}'
 */
// Legacy function for backward compatibility - simplified to avoid circular reference
export const clearAllProjectData = mutation({
  args: {
    confirmationCode: v.string(),
    environment: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Safety check: Require exact confirmation code
    if (args.confirmationCode !== "DELETE_ALL_PROJECT_DATA") {
      throw new Error(
        "❌ Invalid confirmation code. To proceed, use: { confirmationCode: 'DELETE_ALL_PROJECT_DATA' }"
      );
    }

    // Environment safety check
    if (args.environment === "production") {
      throw new Error("❌ This operation is not allowed in production environment!");
    }

    console.log("🚨 LEGACY FUNCTION: Redirecting to comprehensive reset...");
    console.log("💡 Consider using 'comprehensiveReset' function directly for more options");

    // Execute the same logic as comprehensive reset but inline to avoid circular reference
    console.log(`🚨 STARTING COMPREHENSIVE DATABASE RESET...`);
    console.log("⚠️  This operation cannot be undone!");

    const deletionResults = {
      typingIndicators: 0,
      notifications: 0,
      linkPreviews: 0,
      emailTracking: 0,
      messages: 0,
      imageLikes: 0,
      userProjectNotes: 0,
      projectAssignments: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      users: 0,
      fileStorageItems: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // Execute the same deletion logic as comprehensiveReset
      // (This is a simplified version for legacy compatibility)

      // 1. Delete typing indicators
      console.log("🔄 Deleting typing indicators...");
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      for (const indicator of typingIndicators) {
        await ctx.db.delete(indicator._id);
        deletionResults.typingIndicators++;
      }
      console.log(`✅ Deleted ${deletionResults.typingIndicators} typing indicators`);

      // 2. Delete notifications (no dependencies)
      console.log("🔄 Deleting user notifications...");
      const notifications = await ctx.db.query("notifications").collect();
      for (const notification of notifications) {
        await ctx.db.delete(notification._id);
        deletionResults.notifications++;
      }
      console.log(`✅ Deleted ${deletionResults.notifications} notifications`);

      // 3. Delete link previews (no dependencies)
      console.log("🔄 Deleting link preview cache...");
      const linkPreviews = await ctx.db.query("linkPreviews").collect();
      for (const preview of linkPreviews) {
        await ctx.db.delete(preview._id);
        deletionResults.linkPreviews++;
      }
      console.log(`✅ Deleted ${deletionResults.linkPreviews} link previews`);

      // 4. Delete email tracking (references projects/customers but can be deleted early)
      console.log("🔄 Deleting email tracking data...");
      const emailTracking = await ctx.db.query("emailTracking").collect();
      for (const email of emailTracking) {
        await ctx.db.delete(email._id);
        deletionResults.emailTracking++;
      }
      console.log(`✅ Deleted ${deletionResults.emailTracking} email tracking records`);

      // 5. Delete chat messages
      console.log("🔄 Deleting chat messages...");
      const messages = await ctx.db.query("messages").collect();
      for (const message of messages) {
        await ctx.db.delete(message._id);
        deletionResults.messages++;
      }
      console.log(`✅ Deleted ${deletionResults.messages} chat messages`);

      // 6. Delete image likes
      console.log("🔄 Deleting image likes...");
      const imageLikes = await ctx.db.query("imageLikes").collect();
      for (const like of imageLikes) {
        await ctx.db.delete(like._id);
        deletionResults.imageLikes++;
      }
      console.log(`✅ Deleted ${deletionResults.imageLikes} image likes`);

      // 7. Delete user project notes (depends on projects)
      console.log("🔄 Deleting user project notes...");
      const userProjectNotes = await ctx.db.query("userProjectNotes").collect();
      for (const note of userProjectNotes) {
        await ctx.db.delete(note._id);
        deletionResults.userProjectNotes++;
      }
      console.log(`✅ Deleted ${deletionResults.userProjectNotes} user project notes`);

      // 8. Delete project assignments (depends on projects)
      console.log("🔄 Deleting project assignments...");
      const projectAssignments = await ctx.db.query("projectAssignments").collect();
      for (const assignment of projectAssignments) {
        await ctx.db.delete(assignment._id);
        deletionResults.projectAssignments++;
      }
      console.log(`✅ Deleted ${deletionResults.projectAssignments} project assignments`);

      // 9. Delete log entries
      console.log("🔄 Deleting log entries...");
      const logEntries = await ctx.db.query("logEntries").collect();
      for (const entry of logEntries) {
        await ctx.db.delete(entry._id);
        deletionResults.logEntries++;
      }
      console.log(`✅ Deleted ${deletionResults.logEntries} log entries`);

      // 10. Delete projects
      console.log("🔄 Deleting projects...");
      const projects = await ctx.db.query("projects").collect();
      for (const project of projects) {
        await ctx.db.delete(project._id);
        deletionResults.projects++;
      }
      console.log(`✅ Deleted ${deletionResults.projects} projects`);

      // 11. Delete customers
      console.log("🔄 Deleting customers...");
      const customers = await ctx.db.query("customers").collect();
      for (const customer of customers) {
        await ctx.db.delete(customer._id);
        deletionResults.customers++;
      }
      console.log(`✅ Deleted ${deletionResults.customers} customers`);

      // 12. Delete users (if table exists)
      console.log("🔄 Deleting user records...");
      try {
        const users = await ctx.db.query("users").collect();
        for (const user of users) {
          await ctx.db.delete(user._id);
          deletionResults.users++;
        }
        console.log(`✅ Deleted ${deletionResults.users} user records`);
      } catch (error) {
        console.log("ℹ️  Users table not found or empty (this is normal for older databases)");
      }

      // Calculate total
      deletionResults.totalDeleted =
        deletionResults.typingIndicators +
        deletionResults.notifications +
        deletionResults.linkPreviews +
        deletionResults.emailTracking +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.userProjectNotes +
        deletionResults.projectAssignments +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers +
        deletionResults.users;

      console.log("🎉 LEGACY DATABASE RESET COMPLETED SUCCESSFULLY!");
      console.log("📊 DELETION SUMMARY:");
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);
      console.log(`   • TOTAL DELETED: ${deletionResults.totalDeleted} records`);
      console.log("✅ Database is now clean for fresh testing!");

      return {
        success: true,
        message: "All project data has been successfully deleted (legacy function)",
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      deletionResults.errors.push(errorMessage);

      console.error("❌ ERROR DURING LEGACY DELETION PROCESS:", errorMessage);
      console.log("📊 PARTIAL DELETION RESULTS:");
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);

      throw new Error(`Legacy deletion process failed: ${errorMessage}. Partial results: ${JSON.stringify(deletionResults)}`);
    }
  }
});

/**
 * COMPREHENSIVE DATABASE RESET
 *
 * Enhanced version with contractor onboarding support, dry-run capability,
 * and comprehensive safety features.
 */
export const comprehensiveReset = mutation({
  args: {
    confirmationCode: v.string(),
    dryRun: v.optional(v.boolean()), // Preview mode - shows what would be deleted
    includeFileStorage: v.optional(v.boolean()), // Whether to delete files from storage
    environment: v.optional(v.string()) // Environment check
  },
  handler: async (ctx, args) => {
    // Safety check: Require exact confirmation code
    if (args.confirmationCode !== "DELETE_ALL_PROJECT_DATA") {
      throw new Error(
        "❌ Invalid confirmation code. To proceed, use: { confirmationCode: 'DELETE_ALL_PROJECT_DATA' }"
      );
    }

    // Environment safety check
    if (args.environment === "production") {
      throw new Error("❌ This operation is not allowed in production environment!");
    }

    const isDryRun = args.dryRun ?? false;
    const includeFileStorage = args.includeFileStorage ?? false;

    console.log(`🚨 STARTING ${isDryRun ? 'DRY-RUN' : 'COMPREHENSIVE'} DATABASE RESET...`);
    console.log("⚠️  This operation cannot be undone!");
    if (isDryRun) {
      console.log("🔍 DRY-RUN MODE: No data will actually be deleted");
    }

    const deletionResults = {
      typingIndicators: 0,
      notifications: 0,
      linkPreviews: 0,
      emailTracking: 0,
      messages: 0,
      imageLikes: 0,
      userProjectNotes: 0,
      projectAssignments: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      users: 0,
      fileStorageItems: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // 1. Delete typing indicators (no dependencies)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} typing indicators...`);
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      deletionResults.typingIndicators = typingIndicators.length;

      if (!isDryRun) {
        for (const indicator of typingIndicators) {
          await ctx.db.delete(indicator._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.typingIndicators} typing indicators`);

      // 2. Delete notifications (no dependencies)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} user notifications...`);
      const notifications = await ctx.db.query("notifications").collect();
      deletionResults.notifications = notifications.length;

      if (!isDryRun) {
        for (const notification of notifications) {
          await ctx.db.delete(notification._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.notifications} notifications`);

      // 3. Delete link previews (no dependencies)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} link preview cache...`);
      const linkPreviews = await ctx.db.query("linkPreviews").collect();
      deletionResults.linkPreviews = linkPreviews.length;

      if (!isDryRun) {
        for (const preview of linkPreviews) {
          await ctx.db.delete(preview._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.linkPreviews} link previews`);

      // 4. Delete email tracking (references projects/customers but can be deleted early)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} email tracking data...`);
      const emailTracking = await ctx.db.query("emailTracking").collect();
      deletionResults.emailTracking = emailTracking.length;

      if (!isDryRun) {
        for (const email of emailTracking) {
          await ctx.db.delete(email._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.emailTracking} email tracking records`);

      // 5. Delete chat messages and reactions (depends on logEntries)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} chat messages and reactions...`);
      const messages = await ctx.db.query("messages").collect();
      deletionResults.messages = messages.length;

      if (!isDryRun) {
        for (const message of messages) {
          await ctx.db.delete(message._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.messages} chat messages`);

      // 6. Delete image likes and customer session data (depends on logEntries and projects)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} image likes and customer sessions...`);
      const imageLikes = await ctx.db.query("imageLikes").collect();
      deletionResults.imageLikes = imageLikes.length;

      if (!isDryRun) {
        for (const like of imageLikes) {
          await ctx.db.delete(like._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.imageLikes} image likes`);

      // 7. Delete user project notes (depends on projects)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} user project notes...`);
      const userProjectNotes = await ctx.db.query("userProjectNotes").collect();
      deletionResults.userProjectNotes = userProjectNotes.length;

      if (!isDryRun) {
        for (const note of userProjectNotes) {
          await ctx.db.delete(note._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.userProjectNotes} user project notes`);

      // 8. Delete project assignments (depends on projects)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} project assignments...`);
      const projectAssignments = await ctx.db.query("projectAssignments").collect();
      deletionResults.projectAssignments = projectAssignments.length;

      if (!isDryRun) {
        for (const assignment of projectAssignments) {
          await ctx.db.delete(assignment._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.projectAssignments} project assignments`);

      // 9. Delete log entries and associated images (depends on projects)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} log entries and associated images...`);
      const logEntries = await ctx.db.query("logEntries").collect();
      deletionResults.logEntries = logEntries.length;

      if (!isDryRun) {
        for (const entry of logEntries) {
          // Delete associated image from storage if requested
          if (includeFileStorage && entry.imageId) {
            try {
              await ctx.storage.delete(entry.imageId);
              deletionResults.fileStorageItems++;
            } catch (storageError) {
              console.warn(`⚠️  Could not delete image ${entry.imageId}: ${storageError}`);
            }
          }
          await ctx.db.delete(entry._id);
        }
      } else if (includeFileStorage) {
        // Count images that would be deleted in dry-run
        const imagesCount = logEntries.filter(entry => entry.imageId).length;
        deletionResults.fileStorageItems = imagesCount;
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.logEntries} log entries`);
      if (includeFileStorage) {
        console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.fileStorageItems} associated images`);
      }

      // 10. Delete projects including archived ones and job data (depends on customers)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} projects (including archived) and job data...`);
      const projects = await ctx.db.query("projects").collect();
      deletionResults.projects = projects.length;

      if (!isDryRun) {
        for (const project of projects) {
          // Delete job photos from storage if requested
          if (includeFileStorage && project.jobData?.photos) {
            for (const photo of project.jobData.photos) {
              try {
                // Extract storage ID from URL if it's a Convex storage URL
                const storageId = extractStorageIdFromUrl(photo.url);
                if (storageId) {
                  await ctx.storage.delete(storageId);
                  deletionResults.fileStorageItems++;
                }
              } catch (storageError) {
                console.warn(`⚠️  Could not delete job photo ${photo.url}: ${storageError}`);
              }
            }
          }
          await ctx.db.delete(project._id);
        }
      } else if (includeFileStorage) {
        // Count job photos that would be deleted in dry-run
        let jobPhotosCount = 0;
        for (const project of projects) {
          if (project.jobData?.photos) {
            jobPhotosCount += project.jobData.photos.length;
          }
        }
        deletionResults.fileStorageItems += jobPhotosCount;
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.projects} projects`);

      // 11. Delete customers (both regular customers and contractor companies)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} customers (including contractor companies)...`);
      const customers = await ctx.db.query("customers").collect();
      deletionResults.customers = customers.length;

      if (!isDryRun) {
        for (const customer of customers) {
          await ctx.db.delete(customer._id);
        }
      }

      // Count contractor vs regular customers for detailed reporting
      const contractorCustomers = customers.filter(c => c.contractorUserId);
      const regularCustomers = customers.filter(c => !c.contractorUserId);
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.customers} customers (${contractorCustomers.length} contractor companies, ${regularCustomers.length} regular customers)`);

      // 12. Delete user records (contractor onboarding data)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} user records (contractor onboarding data)...`);
      const users = await ctx.db.query("users").collect();
      deletionResults.users = users.length;

      if (!isDryRun) {
        for (const user of users) {
          await ctx.db.delete(user._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.users} user records`);

      // Calculate total
      deletionResults.totalDeleted =
        deletionResults.typingIndicators +
        deletionResults.notifications +
        deletionResults.linkPreviews +
        deletionResults.emailTracking +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.userProjectNotes +
        deletionResults.projectAssignments +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers +
        deletionResults.users +
        deletionResults.fileStorageItems;

      const operationVerb = isDryRun ? 'ANALYSIS' : 'DELETION';
      const operationPastTense = isDryRun ? 'analyzed' : 'deleted';

      console.log(`🎉 COMPREHENSIVE DATABASE ${operationVerb} COMPLETED SUCCESSFULLY!`);
      console.log(`📊 ${operationVerb} SUMMARY:`);
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);
      if (includeFileStorage) {
        console.log(`   • File Storage Items: ${deletionResults.fileStorageItems}`);
      }
      console.log(`   • TOTAL ${operationPastTense.toUpperCase()}: ${deletionResults.totalDeleted} records`);

      if (isDryRun) {
        console.log("🔍 DRY-RUN COMPLETE: No data was actually deleted");
        console.log("💡 To execute the deletion, run again with dryRun: false");
      } else {
        console.log("✅ Database is now completely clean for fresh testing!");
        console.log("🔄 All schema structures and indexes remain intact");
      }

      return {
        success: true,
        message: isDryRun
          ? "Dry-run analysis completed successfully"
          : "All user data has been successfully deleted",
        isDryRun,
        includeFileStorage,
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      deletionResults.errors.push(errorMessage);

      const operationVerb = isDryRun ? 'ANALYSIS' : 'DELETION';
      console.error(`❌ ERROR DURING ${operationVerb} PROCESS:`, errorMessage);
      console.log(`📊 PARTIAL ${operationVerb} RESULTS:`);
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);
      if (includeFileStorage) {
        console.log(`   • File Storage Items: ${deletionResults.fileStorageItems}`);
      }

      if (!isDryRun) {
        console.log("⚠️  IMPORTANT: Some data may have been partially deleted!");
        console.log("⚠️  Database may be in an inconsistent state!");
        console.log("💡 Consider running the operation again to complete the cleanup");
      }

      throw new Error(`${operationVerb} process failed: ${errorMessage}. Partial results: ${JSON.stringify(deletionResults)}`);
    }
  }
});

/**
 * Helper function to extract Convex storage ID from URL
 * Convex storage URLs typically follow the pattern: https://domain.convex.cloud/api/storage/[storageId]
 */
function extractStorageIdFromUrl(url: string): string | null {
  try {
    const match = url.match(/\/api\/storage\/([^/?]+)/);
    return match ? match[1] : null;
  } catch {
    return null;
  }
}

/**
 * DEVELOPMENT UTILITY: Get Comprehensive Data Count
 *
 * This query returns the current count of all user-generated data
 * without deleting anything. Useful for checking database state
 * before and after running the reset script.
 *
 * Enhanced to include contractor onboarding data and detailed breakdowns.
 */
export const getProjectDataCount = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const counts = {
        typingIndicators: 0,
        notifications: 0,
        linkPreviews: 0,
        emailTracking: 0,
        messages: 0,
        imageLikes: 0,
        userProjectNotes: 0,
        projectAssignments: 0,
        logEntries: 0,
        projects: 0,
        customers: 0,
        users: 0,
        total: 0,
        // Detailed breakdowns
        archivedProjects: 0,
        contractorCustomers: 0,
        regularCustomers: 0,
        completedOnboarding: 0,
        pendingOnboarding: 0
      };

      // Count all user-generated data
      counts.typingIndicators = (await ctx.db.query("typingIndicators").collect()).length;
      counts.notifications = (await ctx.db.query("notifications").collect()).length;
      counts.linkPreviews = (await ctx.db.query("linkPreviews").collect()).length;
      counts.emailTracking = (await ctx.db.query("emailTracking").collect()).length;
      counts.messages = (await ctx.db.query("messages").collect()).length;
      counts.imageLikes = (await ctx.db.query("imageLikes").collect()).length;
      counts.userProjectNotes = (await ctx.db.query("userProjectNotes").collect()).length;
      counts.projectAssignments = (await ctx.db.query("projectAssignments").collect()).length;

      const logEntries = await ctx.db.query("logEntries").collect();
      counts.logEntries = logEntries.length;

      const projects = await ctx.db.query("projects").collect();
      counts.projects = projects.length;
      counts.archivedProjects = projects.filter(p => p.isArchived).length;

      const customers = await ctx.db.query("customers").collect();
      counts.customers = customers.length;
      counts.contractorCustomers = customers.filter(c => c.contractorUserId).length;
      counts.regularCustomers = customers.filter(c => !c.contractorUserId).length;

      const users = await ctx.db.query("users").collect();
      counts.users = users.length;
      counts.completedOnboarding = users.filter(u => u.contractorCompleted).length;
      counts.pendingOnboarding = users.filter(u => !u.contractorCompleted).length;

      counts.total = counts.typingIndicators + counts.notifications + counts.linkPreviews +
                    counts.emailTracking + counts.messages + counts.imageLikes +
                    counts.userProjectNotes + counts.projectAssignments + counts.logEntries +
                    counts.projects + counts.customers + counts.users;

      console.log("📊 COMPREHENSIVE DATABASE STATE:");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log("📱 CHAT SYSTEM:");
      console.log(`   • Typing Indicators: ${counts.typingIndicators}`);
      console.log(`   • Chat Messages: ${counts.messages}`);
      console.log(`   • Image Likes: ${counts.imageLikes}`);
      console.log("");
      console.log("🔔 NOTIFICATIONS & TRACKING:");
      console.log(`   • User Notifications: ${counts.notifications}`);
      console.log(`   • Email Tracking: ${counts.emailTracking}`);
      console.log(`   • Link Previews: ${counts.linkPreviews}`);
      console.log("");
      console.log("📋 PROJECT SYSTEM:");
      console.log(`   • Log Entries: ${counts.logEntries}`);
      console.log(`   • Projects: ${counts.projects} (${counts.archivedProjects} archived, ${counts.projects - counts.archivedProjects} active)`);
      console.log(`   • User Project Notes: ${counts.userProjectNotes}`);
      console.log(`   • Project Assignments: ${counts.projectAssignments}`);
      console.log("");
      console.log("👥 USER MANAGEMENT:");
      console.log(`   • User Records: ${counts.users} (${counts.completedOnboarding} completed onboarding, ${counts.pendingOnboarding} pending)`);
      console.log(`   • Customers: ${counts.customers} (${counts.contractorCustomers} contractor companies, ${counts.regularCustomers} regular customers)`);
      console.log("");
      console.log(`🔢 TOTAL RECORDS: ${counts.total}`);
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      return {
        success: true,
        counts,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ ERROR GETTING DATA COUNT:", errorMessage);
      throw new Error(`Failed to get data count: ${errorMessage}`);
    }
  }
});

/**
 * DEVELOPMENT UTILITY: Dry-Run Database Reset
 *
 * Convenience function that runs a dry-run analysis without actually deleting data.
 * Shows exactly what would be deleted if the full reset was executed.
 */
export const dryRunReset = mutation({
  args: {
    includeFileStorage: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const includeFileStorage = args.includeFileStorage ?? false;

    console.log("🔍 STARTING DRY-RUN ANALYSIS...");
    console.log("📊 This will show what would be deleted without actually deleting anything");

    // Use the same logic as comprehensiveReset but with dryRun: true
    const deletionResults = {
      typingIndicators: 0,
      notifications: 0,
      linkPreviews: 0,
      emailTracking: 0,
      messages: 0,
      imageLikes: 0,
      userProjectNotes: 0,
      projectAssignments: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      users: 0,
      fileStorageItems: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // Count all tables (same logic as comprehensiveReset)
      console.log("🔄 Analyzing typing indicators...");
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      deletionResults.typingIndicators = typingIndicators.length;
      console.log(`📊 Found ${deletionResults.typingIndicators} typing indicators`);

      console.log("🔄 Analyzing user notifications...");
      const notifications = await ctx.db.query("notifications").collect();
      deletionResults.notifications = notifications.length;
      console.log(`📊 Found ${deletionResults.notifications} notifications`);

      console.log("🔄 Analyzing link preview cache...");
      const linkPreviews = await ctx.db.query("linkPreviews").collect();
      deletionResults.linkPreviews = linkPreviews.length;
      console.log(`📊 Found ${deletionResults.linkPreviews} link previews`);

      console.log("🔄 Analyzing email tracking data...");
      const emailTracking = await ctx.db.query("emailTracking").collect();
      deletionResults.emailTracking = emailTracking.length;
      console.log(`📊 Found ${deletionResults.emailTracking} email tracking records`);

      console.log("🔄 Analyzing chat messages...");
      const messages = await ctx.db.query("messages").collect();
      deletionResults.messages = messages.length;
      console.log(`📊 Found ${deletionResults.messages} chat messages`);

      console.log("🔄 Analyzing image likes...");
      const imageLikes = await ctx.db.query("imageLikes").collect();
      deletionResults.imageLikes = imageLikes.length;
      console.log(`📊 Found ${deletionResults.imageLikes} image likes`);

      console.log("🔄 Analyzing user project notes...");
      const userProjectNotes = await ctx.db.query("userProjectNotes").collect();
      deletionResults.userProjectNotes = userProjectNotes.length;
      console.log(`📊 Found ${deletionResults.userProjectNotes} user project notes`);

      console.log("🔄 Analyzing project assignments...");
      const projectAssignments = await ctx.db.query("projectAssignments").collect();
      deletionResults.projectAssignments = projectAssignments.length;
      console.log(`📊 Found ${deletionResults.projectAssignments} project assignments`);

      console.log("🔄 Analyzing log entries...");
      const logEntries = await ctx.db.query("logEntries").collect();
      deletionResults.logEntries = logEntries.length;
      console.log(`📊 Found ${deletionResults.logEntries} log entries`);

      console.log("🔄 Analyzing projects...");
      const projects = await ctx.db.query("projects").collect();
      deletionResults.projects = projects.length;
      console.log(`📊 Found ${deletionResults.projects} projects`);

      console.log("🔄 Analyzing customers...");
      const customers = await ctx.db.query("customers").collect();
      deletionResults.customers = customers.length;
      console.log(`📊 Found ${deletionResults.customers} customers`);

      console.log("🔄 Analyzing users...");
      const users = await ctx.db.query("users").collect();
      deletionResults.users = users.length;
      console.log(`📊 Found ${deletionResults.users} users`);

      // Calculate total
      deletionResults.totalDeleted =
        deletionResults.typingIndicators +
        deletionResults.notifications +
        deletionResults.linkPreviews +
        deletionResults.emailTracking +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.userProjectNotes +
        deletionResults.projectAssignments +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers +
        deletionResults.users;

      console.log(`📊 TOTAL RECORDS TO DELETE: ${deletionResults.totalDeleted}`);
      console.log("🔍 DRY-RUN COMPLETE: No data was actually deleted");

      return {
        success: true,
        message: "Dry-run analysis completed successfully",
        isDryRun: true,
        includeFileStorage,
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ Dry-run analysis failed: ${errorMessage}`);
      deletionResults.errors.push(errorMessage);

      throw new Error(`Dry-run analysis failed: ${errorMessage}`);
    }
  }
});
