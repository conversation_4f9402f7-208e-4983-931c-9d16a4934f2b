import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { api } from "./_generated/api";

// Check JobbLogg registration status for BRREG companies
export const checkJobbLoggRegistrationStatus = query({
  args: {
    companies: v.array(v.object({
      organizationNumber: v.string(),
      name: v.string(),
    })),
    requestedBy: v.string(),
  },
  handler: async (ctx, args) => {
    // Verify requesting user is an administrator
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.requestedBy))
      .first();

    if (!requestingUser || requestingUser.role !== "administrator") {
      throw new Error("Kun administratorer kan sjekke registreringsstatus");
    }

    // Check registration status for each company
    const statusResults = await Promise.all(
      args.companies.map(async (company) => {
        // Check if company exists in JobbLogg as contractor company
        const contractorCompany = await ctx.db
          .query("customers")
          .withIndex("by_org_number", (q) => q.eq("orgNumber", company.organizationNumber))
          .filter((q) => q.neq(q.field("contractorUserId"), undefined))
          .first();

        // Check if company exists as regular customer (bedrift type)
        const customerCompany = await ctx.db
          .query("customers")
          .withIndex("by_org_number", (q) => q.eq("orgNumber", company.organizationNumber))
          .filter((q) => q.eq(q.field("type"), "bedrift"))
          .first();

        const isRegistered = !!(contractorCompany || customerCompany);
        const registeredCompany = contractorCompany || customerCompany;

        // If registered as contractor, get additional details
        let contractorDetails = null;
        if (contractorCompany && contractorCompany.contractorUserId) {
          const contractorUser = await ctx.db
            .query("users")
            .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", contractorCompany.contractorUserId!))
            .first();

          if (contractorUser) {
            contractorDetails = {
              userId: contractorUser._id,
              clerkUserId: contractorUser.clerkUserId,
              role: contractorUser.role,
              specializations: [
                ...(contractorCompany.primarySpecialization ? [contractorCompany.primarySpecialization] : []),
                ...(contractorCompany.secondarySpecializations || [])
              ],
            };
          }
        }

        return {
          organizationNumber: company.organizationNumber,
          name: company.name,
          isRegistered,
          registrationType: contractorCompany ? 'contractor' : customerCompany ? 'customer' : null,
          jobbLoggCompany: registeredCompany ? {
            id: registeredCompany._id,
            name: registeredCompany.name,
            contactPerson: registeredCompany.contactPerson,
            phone: registeredCompany.phone,
            email: registeredCompany.email,
            specializations: [
              ...(registeredCompany.primarySpecialization ? [registeredCompany.primarySpecialization] : []),
              ...(registeredCompany.secondarySpecializations || [])
            ],
          } : null,
          contractorDetails,
        };
      })
    );

    return statusResults;
  },
});



// Get detailed collaboration history between a main contractor and subcontractor company
export const getCollaborationHistory = query({
  args: {
    mainContractorUserId: v.string(), // Clerk ID of main contractor
    subcontractorCompanyId: v.id("customers"),
    currentProjectId: v.optional(v.id("projects")), // For context-aware display
  },
  handler: async (ctx, args) => {
    // Verify requesting user is an administrator
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.mainContractorUserId))
      .first();

    if (!requestingUser || requestingUser.role !== "administrator") {
      throw new Error("Kun administratorer kan se samarbeidshistorikk");
    }

    // Get main contractor's company
    const mainContractorCompany = requestingUser.contractorCompanyId;
    if (!mainContractorCompany) {
      throw new Error("Bruker har ikke tilknyttet bedrift");
    }

    // Get all project assignments for this subcontractor company
    const allAssignments = await ctx.db
      .query("projectAssignments")
      .filter(q => q.and(
        q.eq(q.field("assignedCompanyId"), args.subcontractorCompanyId),
        q.eq(q.field("isSubcontractor"), true),
        q.eq(q.field("isActive"), true)
      ))
      .collect();

    if (allAssignments.length === 0) {
      return {
        hasCollaborated: false,
        totalProjects: 0,
        currentProjectHistory: null,
        otherProjectsHistory: {
          projectCount: 0,
          recentProjects: [],
          specializations: [],
          successfulCompletions: 0,
        },
        qualityMetrics: null,
      };
    }

    // Get project details for all assignments
    const projectsWithDetails = await Promise.all(
      allAssignments.map(async (assignment) => {
        const project = await ctx.db.get(assignment.projectId);
        if (!project) return null;

        // Only include projects from the same main contractor
        if (project.userId !== args.mainContractorUserId) return null;

        return {
          assignment,
          project,
        };
      })
    );

    const validProjects = projectsWithDetails.filter(Boolean) as Array<{
      assignment: any;
      project: any;
    }>;

    if (validProjects.length === 0) {
      return {
        hasCollaborated: false,
        totalProjects: 0,
        currentProjectHistory: null,
        otherProjectsHistory: {
          projectCount: 0,
          recentProjects: [],
          specializations: [],
          successfulCompletions: 0,
        },
        qualityMetrics: null,
      };
    }

    // Separate current project from other projects
    const currentProjectAssignments = args.currentProjectId 
      ? validProjects.filter(p => p.project._id === args.currentProjectId)
      : [];
    
    const otherProjects = args.currentProjectId
      ? validProjects.filter(p => p.project._id !== args.currentProjectId)
      : validProjects;

    // Calculate collaboration metrics
    const allTimestamps = validProjects.map(p => p.assignment._creationTime);
    const firstCollaboration = Math.min(...allTimestamps);
    const lastCollaboration = Math.max(...allTimestamps);

    // Get specializations used
    const specializations = [...new Set(
      validProjects
        .map(p => p.assignment.subcontractorSpecialization)
        .filter(Boolean)
    )];

    // Calculate completion metrics
    const completedProjects = validProjects.filter(p => 
      p.project.isArchived || p.assignment.invitationStatus === 'accepted'
    );

    // Build current project history
    const currentProjectHistory = currentProjectAssignments.length > 0 ? {
      hasWorkedOnCurrentProject: true,
      previousAssignments: currentProjectAssignments.map(p => ({
        assignmentId: p.assignment._id,
        specialization: p.assignment.subcontractorSpecialization,
        assignedAt: p.assignment.assignedAt || p.assignment._creationTime,
        status: p.assignment.invitationStatus || 'active',
        assignedBy: p.assignment.assignedBy,
        notes: p.assignment.notes,
      })),
      currentStatus: currentProjectAssignments[0].project.isArchived 
        ? 'archived' as const
        : 'active' as const,
    } : null;

    // Build other projects history
    const recentProjects = otherProjects
      .sort((a, b) => b.assignment._creationTime - a.assignment._creationTime)
      .slice(0, 5)
      .map(p => ({
        projectId: p.project._id,
        projectName: p.project.name,
        specialization: p.assignment.subcontractorSpecialization || 'Ikke spesifisert',
        startDate: p.assignment.assignedAt || p.assignment._creationTime,
        endDate: p.project.archivedAt,
        status: p.project.isArchived 
          ? 'archived' as const 
          : (p.assignment.invitationStatus === 'accepted' ? 'completed' as const : 'active' as const),
        role: p.assignment.accessLevel || 'subcontractor',
      }));

    // Calculate quality metrics
    const projectDurations = validProjects
      .filter(p => p.project.archivedAt)
      .map(p => p.project.archivedAt! - (p.assignment.assignedAt || p.assignment._creationTime));
    
    const averageProjectDuration = projectDurations.length > 0 
      ? projectDurations.reduce((sum, duration) => sum + duration, 0) / projectDurations.length
      : 0;

    const completionRate = validProjects.length > 0 
      ? (completedProjects.length / validProjects.length) * 100
      : 0;

    // Calculate average response time for invitations
    const invitationResponses = validProjects.filter(p => 
      p.assignment.invitedAt && p.assignment.respondedAt
    );
    
    const averageResponseTime = invitationResponses.length > 0
      ? invitationResponses.reduce((sum, p) => 
          sum + (p.assignment.respondedAt! - p.assignment.invitedAt!), 0
        ) / invitationResponses.length
      : 0;

    return {
      hasCollaborated: true,
      totalProjects: validProjects.length,
      firstCollaboration,
      lastCollaboration,
      currentProjectHistory,
      otherProjectsHistory: {
        projectCount: otherProjects.length,
        recentProjects,
        specializations,
        successfulCompletions: completedProjects.length,
      },
      qualityMetrics: {
        averageProjectDuration,
        completionRate,
        responseTime: averageResponseTime,
      },
    };
  },
});

// Get collaboration summary for search results (lightweight version)
export const getCollaborationSummary = query({
  args: {
    mainContractorUserId: v.string(),
    subcontractorCompanyId: v.id("customers"),
    currentProjectId: v.optional(v.id("projects")),
  },
  handler: async (ctx, args) => {
    // Verify requesting user
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.mainContractorUserId))
      .first();

    if (!requestingUser || requestingUser.role !== "administrator") {
      return {
        hasCollaborated: false,
        collaborationType: 'none' as const,
        projectCount: 0,
        primarySpecialization: null,
        lastCollaboration: null,
      };
    }

    // Get assignments for this subcontractor from this main contractor
    const assignments = await ctx.db
      .query("projectAssignments")
      .filter(q => q.and(
        q.eq(q.field("assignedCompanyId"), args.subcontractorCompanyId),
        q.eq(q.field("isSubcontractor"), true),
        q.eq(q.field("isActive"), true)
      ))
      .collect();

    if (assignments.length === 0) {
      return {
        hasCollaborated: false,
        collaborationType: 'none' as const,
        projectCount: 0,
        primarySpecialization: null,
        lastCollaboration: null,
      };
    }

    // Filter assignments to only include projects from this main contractor
    const relevantAssignments = [];
    for (const assignment of assignments) {
      const project = await ctx.db.get(assignment.projectId);
      if (project && project.userId === args.mainContractorUserId) {
        relevantAssignments.push({ assignment, project });
      }
    }

    if (relevantAssignments.length === 0) {
      return {
        hasCollaborated: false,
        collaborationType: 'none' as const,
        projectCount: 0,
        primarySpecialization: null,
        lastCollaboration: null,
      };
    }

    // Determine collaboration type
    const hasCurrentProject = args.currentProjectId && 
      relevantAssignments.some(ra => ra.project._id === args.currentProjectId);
    
    const hasOtherProjects = relevantAssignments.some(ra => 
      !args.currentProjectId || ra.project._id !== args.currentProjectId
    );

    let collaborationType: 'current_project' | 'other_projects' | 'both' | 'none';
    if (hasCurrentProject && hasOtherProjects) {
      collaborationType = 'both';
    } else if (hasCurrentProject) {
      collaborationType = 'current_project';
    } else if (hasOtherProjects) {
      collaborationType = 'other_projects';
    } else {
      collaborationType = 'none';
    }

    // Get most common specialization
    const specializations = relevantAssignments
      .map(ra => ra.assignment.subcontractorSpecialization)
      .filter(Boolean);
    
    const specializationCounts = specializations.reduce((acc, spec) => {
      if (spec) {
        acc[spec] = (acc[spec] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const primarySpecialization = Object.keys(specializationCounts).length > 0
      ? Object.entries(specializationCounts).sort(([,a], [,b]) => b - a)[0][0]
      : null;

    // Get last collaboration timestamp
    const lastCollaboration = Math.max(
      ...relevantAssignments.map(ra => ra.assignment._creationTime)
    );

    return {
      hasCollaborated: true,
      collaborationType,
      projectCount: relevantAssignments.length,
      primarySpecialization,
      lastCollaboration,
    };
  },
});

// Enhanced search function that includes collaboration context
export const searchSubcontractorsWithCollaboration = query({
  args: {
    searchTerm: v.optional(v.string()),
    specialization: v.optional(v.string()),
    location: v.optional(v.string()),
    onlyPreviouslyWorkedWith: v.optional(v.boolean()),
    requestedBy: v.string(),
    currentProjectId: v.optional(v.id("projects")),
  },
  handler: async (ctx, args) => {
    // Verify requesting user is an administrator
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.requestedBy))
      .first();

    if (!requestingUser || requestingUser.role !== "administrator") {
      throw new Error("Kun administratorer kan søke etter underleverandører");
    }

    // Get all contractor companies (potential subcontractors)
    const contractorCompanies = await ctx.db
      .query("customers")
      .filter(q => q.eq(q.field("type"), "contractor"))
      .collect();

    const bedriftCompanies = await ctx.db
      .query("customers")
      .filter(q => q.eq(q.field("type"), "bedrift"))
      .collect();

    const companies = [...contractorCompanies, ...bedriftCompanies];

    // Filter out the requesting user's own company
    const filteredCompanies = companies.filter(company =>
      company._id !== requestingUser.contractorCompanyId
    );

    // Apply search filters
    let results = filteredCompanies;

    if (args.searchTerm) {
      const searchLower = args.searchTerm.toLowerCase();
      results = results.filter(company =>
        company.name.toLowerCase().includes(searchLower) ||
        (company.contactPerson && company.contactPerson.toLowerCase().includes(searchLower))
      );
    }

    // Get basic company information
    const companiesWithUsers = await Promise.all(
      results.map(async (company) => {
        const users = await ctx.db
          .query("users")
          .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", company._id))
          .collect();

        const activeUsers = users.filter(user => user.isActive !== false);

        // Get specializations from company record
        const specializations: string[] = [];
        if (company.primarySpecialization) {
          specializations.push(company.primarySpecialization);
        }
        if (company.secondarySpecializations) {
          specializations.push(...company.secondarySpecializations);
        }

        return {
          id: company._id,
          name: company.name,
          type: company.type,
          contactPerson: company.contactPerson,
          address: company.address,
          userCount: activeUsers.length,
          specializations,
        };
      })
    );

    // Apply specialization filter
    let filteredResults = companiesWithUsers;
    if (args.specialization) {
      filteredResults = filteredResults.filter(company =>
        company.specializations.includes(args.specialization!)
      );
    }

    // Check actual collaboration data for each company
    const enhancedResults = await Promise.all(
      filteredResults.map(async (company) => {
        // Check if there are any actual assignments for this company
        const assignments = await ctx.db
          .query("projectAssignments")
          .filter(q => q.and(
            q.eq(q.field("assignedCompanyId"), company.id),
            q.eq(q.field("isSubcontractor"), true),
            q.eq(q.field("isActive"), true)
          ))
          .collect();

        // Filter assignments to only include projects from this main contractor
        const relevantAssignments = [];
        for (const assignment of assignments) {
          const project = await ctx.db.get(assignment.projectId);
          if (project && project.userId === args.requestedBy) {
            relevantAssignments.push(assignment);
          }
        }

        const hasCollaborated = relevantAssignments.length > 0;
        const currentProjectAssignments = relevantAssignments.filter(a => a.projectId === args.currentProjectId);

        let collaborationType: 'current_project' | 'both' | 'other_projects' | 'none' = 'none';
        if (currentProjectAssignments.length > 0 && relevantAssignments.length > currentProjectAssignments.length) {
          collaborationType = 'both';
        } else if (currentProjectAssignments.length > 0) {
          collaborationType = 'current_project';
        } else if (relevantAssignments.length > 0) {
          collaborationType = 'other_projects';
        }

        return {
          ...company,
          collaboration: {
            hasCollaborated,
            collaborationType,
            projectCount: relevantAssignments.length,
            primarySpecialization: relevantAssignments[0]?.subcontractorSpecialization || company.specializations?.[0] || null,
            lastCollaboration: relevantAssignments.length > 0 ? Math.max(...relevantAssignments.map(a => a.assignedAt || a._creationTime)) : null,
          },
        };
      })
    );

    // Apply "only previously worked with" filter if requested
    let finalResults = enhancedResults;
    if (args.onlyPreviouslyWorkedWith) {
      finalResults = enhancedResults.filter(company => company.collaboration.hasCollaborated);
    }

    // Sort by collaboration relevance
    finalResults.sort((a, b) => {
      // Prioritize previous collaborations
      if (a.collaboration.hasCollaborated && !b.collaboration.hasCollaborated) return -1;
      if (!a.collaboration.hasCollaborated && b.collaboration.hasCollaborated) return 1;

      // Finally alphabetical
      return a.name.localeCompare(b.name);
    });

    return finalResults.slice(0, 20); // Limit results
  },
});
