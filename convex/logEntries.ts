import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

// Generate upload URL for file storage
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  }
});

// Create a new log entry with optional image
export const create = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage"))
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Verify the project exists
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Project not found");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Check if project is archived
    if (project.isArchived) {
      throw new Error("Cannot add log entries to archived projects");
    }

    return await ctx.db.insert("logEntries", {
      projectId: args.projectId,
      userId: args.userId,
      description: args.description,
      imageId: args.imageId,
      entryType: "user",
      createdAt: Date.now()
    });
  }
});

// Get all log entries for a specific project
export const getByProject = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      // Return empty array instead of throwing error to handle deleted projects gracefully
      return [];
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    const logEntries = await ctx.db
      .query("logEntries")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Get image URLs for entries that have images
    const logEntriesWithImages = await Promise.all(
      logEntries.map(async (entry) => {
        if (entry.imageId) {
          const imageUrl = await ctx.storage.getUrl(entry.imageId);
          return {
            ...entry,
            imageUrl
          };
        }
        return {
          ...entry,
          imageUrl: null
        };
      })
    );

    return logEntriesWithImages.sort((a, b) => b.createdAt - a.createdAt);
  }
});

// Get all log entries for a user across all projects
export const getByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const logEntries = await ctx.db
      .query("logEntries")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Get image URLs for entries that have images
    const logEntriesWithImages = await Promise.all(
      logEntries.map(async (entry) => {
        if (entry.imageId) {
          const imageUrl = await ctx.storage.getUrl(entry.imageId);
          return {
            ...entry,
            imageUrl
          };
        }
        return {
          ...entry,
          imageUrl: null
        };
      })
    );

    return logEntriesWithImages.sort((a, b) => b.createdAt - a.createdAt);
  }
});

// Get a specific log entry by ID (for chat system)
export const getById = query({
  args: {
    logEntryId: v.id("logEntries"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the log entry
    const entry = await ctx.db.get(args.logEntryId);
    if (!entry) {
      return null;
    }

    // Get the project to verify access
    const project = await ctx.db.get(entry.projectId);
    if (!project) {
      return null;
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: project._id,
    });

    // Allow access if user has project access OR is the entry owner
    const hasAccess = userAccess.hasAccess || entry.userId === args.userId;
    if (!hasAccess) {
      throw new Error("Ingen tilgang til denne loggføringen");
    }

    // Add image URL if present
    let imageUrl = null;
    if (entry.imageId) {
      imageUrl = await ctx.storage.getUrl(entry.imageId);
    }

    return {
      ...entry,
      imageUrl
    };
  }
});

// Edit a log entry with version history tracking
export const editLogEntry = mutation({
  args: {
    entryId: v.id("logEntries"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    changeType: v.union(v.literal("description"), v.literal("image"), v.literal("both")),
    changeSummary: v.string()
  },
  handler: async (ctx, args) => {
    // Get the log entry
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Log entry not found");
    }

    // Verify ownership
    if (entry.userId !== args.userId) {
      throw new Error("Unauthorized: Log entry does not belong to user");
    }

    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Verify the project still exists
    const project = await ctx.db.get(entry.projectId);
    if (!project) {
      throw new Error("Project not found");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: entry.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Check if project is archived
    if (project.isArchived) {
      throw new Error("Cannot edit log entries in archived projects");
    }

    const now = Date.now();

    // Initialize edit history if this is the first edit
    let editHistory = entry.editHistory || [];

    // If this is the first edit, save the original as version 1
    if (!entry.isEdited) {
      editHistory.push({
        version: 1,
        editedAt: entry.createdAt,
        description: entry.description,
        imageId: entry.imageId,
        changeType: "description" as const,
        changeSummary: "Opprinnelig versjon"
      });
    }

    // Add the new version to history
    const newVersion = editHistory.length + 1;
    editHistory.push({
      version: newVersion,
      editedAt: now,
      description: args.description,
      imageId: args.imageId,
      changeType: args.changeType,
      changeSummary: args.changeSummary
    });

    // Prepare update object
    const updateData: any = {
      description: args.description,
      isEdited: true,
      lastEditedAt: now,
      editHistory: editHistory
    };

    // Only update imageId if it's provided in args (meaning image is being changed)
    if (args.imageId !== undefined) {
      updateData.imageId = args.imageId;
    }

    // Update the log entry with new content and edit metadata
    await ctx.db.patch(args.entryId, updateData);

    return { success: true, version: newVersion };
  }
});

// Get edit history for a log entry (read-only access for transparency)
export const getLogEntryHistory = query({
  args: {
    entryId: v.id("logEntries"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the log entry
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Log entry not found");
    }

    // Get the project to verify access
    const project = await ctx.db.get(entry.projectId);
    if (!project) {
      throw new Error("Project not found");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: project._id,
    });

    // Allow access if user has project access OR is the entry owner
    // This enables customers to view edit history for transparency
    const hasAccess = userAccess.hasAccess || entry.userId === args.userId;
    if (!hasAccess) {
      throw new Error("Ingen tilgang til denne loggføringen");
    }

    // Return edit history with image URLs
    if (!entry.editHistory) {
      return [];
    }

    const historyWithImages = await Promise.all(
      entry.editHistory.map(async (version) => {
        let imageUrl = null;
        if (version.imageId) {
          imageUrl = await ctx.storage.getUrl(version.imageId);
        }
        return {
          ...version,
          imageUrl
        };
      })
    );

    return historyWithImages.sort((a, b) => b.version - a.version);
  }
});

// Get log entries for a shared project (public access)
export const getBySharedProject = query({
  args: { sharedId: v.string() },
  handler: async (ctx, args) => {
    // Find the project by shared ID
    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();

    if (!project) {
      return [];
    }

    // Check if sharing is enabled
    if (!project.isPubliclyShared) {
      return [];
    }

    // Note: Expiration feature was removed from schema

    // Get log entries for the project
    const logEntries = await ctx.db
      .query("logEntries")
      .withIndex("by_project", (q) => q.eq("projectId", project._id))
      .collect();

    // Get image URLs for entries that have images
    const logEntriesWithImages = await Promise.all(
      logEntries.map(async (entry) => {
        if (entry.imageId) {
          const imageUrl = await ctx.storage.getUrl(entry.imageId);
          return {
            ...entry,
            imageUrl
          };
        }
        return {
          ...entry,
          imageUrl: null
        };
      })
    );

    return logEntriesWithImages.sort((a, b) => b.createdAt - a.createdAt);
  }
});

// Get specific log entry for shared project chat (public access)
export const getBySharedProjectLogId = query({
  args: {
    sharedId: v.string(),
    logId: v.id("logEntries")
  },
  handler: async (ctx, args) => {
    // Find the project by shared ID
    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();

    if (!project) {
      return null;
    }

    // Check if sharing is enabled
    if (!project.isPubliclyShared) {
      return null;
    }

    // Get the specific log entry
    const entry = await ctx.db.get(args.logId);
    if (!entry || entry.projectId !== project._id) {
      return null;
    }

    // Add image URL if present
    let imageUrl = null;
    if (entry.imageId) {
      imageUrl = await ctx.storage.getUrl(entry.imageId);
    }

    return {
      ...entry,
      imageUrl
    };
  }
});

// Get edit history for shared project (public read-only access)
export const getLogEntryHistoryShared = query({
  args: {
    entryId: v.id("logEntries"),
    sharedId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the log entry
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Log entry not found");
    }

    // Get the project and verify it's shared
    const project = await ctx.db.get(entry.projectId);
    if (!project) {
      throw new Error("Project not found");
    }

    // Verify this is the correct shared project
    if (project.sharedId !== args.sharedId) {
      throw new Error("Unauthorized access");
    }

    // Check if sharing is enabled
    if (!project.isPubliclyShared) {
      throw new Error("Project is not publicly shared");
    }

    // Note: Expiration feature was removed from schema

    // Return edit history with image URLs
    if (!entry.editHistory) {
      return [];
    }

    const historyWithImages = await Promise.all(
      entry.editHistory.map(async (version) => {
        let imageUrl = null;
        if (version.imageId) {
          imageUrl = await ctx.storage.getUrl(version.imageId);
        }
        return {
          ...version,
          imageUrl
        };
      })
    );

    return historyWithImages.sort((a, b) => b.version - a.version);
  }
});

// Get a specific version of a log entry (read-only access for transparency)
export const getLogEntryVersion = query({
  args: {
    entryId: v.id("logEntries"),
    userId: v.string(),
    version: v.number()
  },
  handler: async (ctx, args) => {
    // Get the log entry
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Log entry not found");
    }

    // Get the project to verify access
    const project = await ctx.db.get(entry.projectId);
    if (!project) {
      throw new Error("Project not found");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: project._id,
    });

    // Allow access if user has project access OR is the entry owner
    // This enables customers to view edit history for transparency
    const hasAccess = userAccess.hasAccess || entry.userId === args.userId;
    if (!hasAccess) {
      throw new Error("Ingen tilgang til denne loggføringen");
    }

    // Find the requested version
    if (!entry.editHistory) {
      throw new Error("No edit history available");
    }

    const version = entry.editHistory.find(v => v.version === args.version);
    if (!version) {
      throw new Error("Version not found");
    }

    // Get image URL if version has an image
    let imageUrl = null;
    if (version.imageId) {
      imageUrl = await ctx.storage.getUrl(version.imageId);
    }

    return {
      ...version,
      imageUrl
    };
  }
});

// Delete a log entry (and its associated image if it exists)
export const deleteEntry = mutation({
  args: {
    entryId: v.id("logEntries"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Log entry not found");
    }
    if (entry.userId !== args.userId) {
      throw new Error("Unauthorized: Log entry does not belong to user");
    }

    // Delete all images from storage (current and historical)
    const imagesToDelete = [];

    // Add current image if it exists
    if (entry.imageId) {
      imagesToDelete.push(entry.imageId);
    }

    // Add historical images if they exist
    if (entry.editHistory) {
      for (const version of entry.editHistory) {
        if (version.imageId && !imagesToDelete.includes(version.imageId)) {
          imagesToDelete.push(version.imageId);
        }
      }
    }

    // Delete all images
    for (const imageId of imagesToDelete) {
      try {
        await ctx.storage.delete(imageId);
      } catch (error) {
        // Continue deleting other images even if one fails
        console.error(`Failed to delete image ${imageId}:`, error);
      }
    }

    // Delete the log entry
    await ctx.db.delete(args.entryId);
  }
});
