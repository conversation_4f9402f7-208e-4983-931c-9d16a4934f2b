import { mutation } from "./_generated/server";

/**
 * Migration to remove activityCount field from users table
 * This field was removed from the schema but exists in some documents
 */
export const removeActivityCountFromUsers = mutation({
  args: {},
  handler: async (ctx) => {
    // Get all users that have activityCount field
    const users = await ctx.db.query("users").collect();
    
    let updatedCount = 0;
    
    for (const user of users) {
      // Check if user has activityCount field
      if ('activityCount' in user) {
        // Remove the activityCount field by patching without it
        const { activityCount, ...userWithoutActivityCount } = user as any;
        
        // Update the user document
        await ctx.db.replace(user._id, {
          ...userWithoutActivityCount,
          updatedAt: Date.now(),
        });
        
        updatedCount++;
      }
    }
    
    return {
      success: true,
      message: `Removed activityCount from ${updatedCount} users`,
      updatedCount,
    };
  },
});
