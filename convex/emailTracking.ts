import { mutation, query, internalMutation } from './_generated/server';
import { v } from 'convex/values';

// Create email tracking record when email is sent (internal function)
export const createEmailTrackingRecord = internalMutation({
  args: {
    emailId: v.optional(v.string()),
    trackingId: v.optional(v.string()),
    emailType: v.union(
      v.literal("customer_notification"),
      v.literal("team_invitation"),
      v.literal("magic_link_invitation")
    ),
    recipientEmail: v.string(),
    recipientName: v.optional(v.string()),
    subject: v.string(),
    templateUsed: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("sent"),
      v.literal("delivered"),
      v.literal("bounced"),
      v.literal("failed"),
      v.literal("complained")
    ),
    userId: v.string(),
    projectId: v.optional(v.id("projects")),
    customerId: v.optional(v.id("customers")),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const trackingRecordId = await ctx.db.insert("emailTracking", {
      emailId: args.emailId,
      trackingId: args.trackingId,
      emailType: args.emailType,
      recipientEmail: args.recipientEmail,
      recipientName: args.recipientName,
      subject: args.subject,
      templateUsed: args.templateUsed,
      status: args.status,
      sentAt: now,
      lastStatusUpdate: now,
      userId: args.userId,
      projectId: args.projectId,
      customerId: args.customerId,
      errorMessage: args.errorMessage,
    });

    console.log(`📧 Email tracking record created: ${trackingRecordId} for ${args.recipientEmail}`);
    return trackingRecordId;
  },
});

// Update email tracking status (for webhooks or manual updates) - internal function
export const updateEmailStatus = internalMutation({
  args: {
    emailId: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("sent"),
      v.literal("delivered"),
      v.literal("bounced"),
      v.literal("failed"),
      v.literal("complained")
    ),
    errorMessage: v.optional(v.string()),
    bounceReason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Find the tracking record by emailId
    const trackingRecord = await ctx.db
      .query("emailTracking")
      .withIndex("by_email_id", (q) => q.eq("emailId", args.emailId))
      .first();

    if (!trackingRecord) {
      console.error(`❌ Email tracking record not found for emailId: ${args.emailId}`);
      return { success: false, error: "Tracking record not found" };
    }

    // Prepare update data
    const updateData: any = {
      status: args.status,
      lastStatusUpdate: now,
    };

    // Set specific timestamps based on status
    if (args.status === "delivered") {
      updateData.deliveredAt = now;
    } else if (args.status === "bounced") {
      updateData.bouncedAt = now;
      updateData.bounceReason = args.bounceReason;
    }

    if (args.errorMessage) {
      updateData.errorMessage = args.errorMessage;
    }

    // Update the tracking record
    await ctx.db.patch(trackingRecord._id, updateData);

    console.log(`📧 Email status updated: ${args.emailId} -> ${args.status}`);
    return { success: true };
  },
});

// Get email tracking statistics for a user
export const getEmailStats = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const emailRecords = await ctx.db
      .query("emailTracking")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const stats = {
      total: emailRecords.length,
      sent: 0,
      delivered: 0,
      bounced: 0,
      failed: 0,
      pending: 0,
      byType: {
        customer_notification: 0,
        team_invitation: 0,
        magic_link_invitation: 0,
      },
      recentEmails: emailRecords
        .sort((a, b) => b.sentAt - a.sentAt)
        .slice(0, 10)
        .map(record => ({
          id: record._id,
          recipientEmail: record.recipientEmail,
          subject: record.subject,
          status: record.status,
          sentAt: record.sentAt,
          emailType: record.emailType,
        })),
    };

    // Calculate statistics
    emailRecords.forEach(record => {
      stats.byType[record.emailType]++;
      
      switch (record.status) {
        case "sent":
          stats.sent++;
          break;
        case "delivered":
          stats.delivered++;
          break;
        case "bounced":
          stats.bounced++;
          break;
        case "failed":
          stats.failed++;
          break;
        case "pending":
          stats.pending++;
          break;
      }
    });

    return stats;
  },
});

// Get email tracking records for a specific project
export const getProjectEmailTracking = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const emailRecords = await ctx.db
      .query("emailTracking")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    return emailRecords.map(record => ({
      id: record._id,
      recipientEmail: record.recipientEmail,
      recipientName: record.recipientName,
      subject: record.subject,
      status: record.status,
      sentAt: record.sentAt,
      deliveredAt: record.deliveredAt,
      bouncedAt: record.bouncedAt,
      openedAt: record.openedAt,
      clickedAt: record.clickedAt,
      openCount: record.openCount,
      clickCount: record.clickCount,
      errorMessage: record.errorMessage,
      bounceReason: record.bounceReason,
    }));
  },
});

// Get failed emails that might need retry
export const getFailedEmails = query({
  args: { 
    userId: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("emailTracking")
      .withIndex("by_status", (q) => q.eq("status", "failed"));

    if (args.userId) {
      // Filter by user if specified
      const allFailed = await query.collect();
      const userFailed = allFailed.filter(record => record.userId === args.userId);
      return userFailed.slice(0, args.limit || 50);
    }

    const failedEmails = await query.collect();
    return failedEmails.slice(0, args.limit || 50);
  },
});

// Mark email as opened (for engagement tracking)
// REMOVED: Email open tracking is unreliable and has been replaced with project access tracking

// Mark project as accessed by customer (reliable engagement tracking)
export const markProjectAccessed = internalMutation({
  args: {
    projectId: v.id('projects')
  },
  handler: async (ctx, args) => {
    const { projectId } = args;
    const now = Date.now();

    console.log(`📊 Marking project accessed for project: ${projectId}`);

    // Find all email tracking records for this project
    const emailRecords = await ctx.db
      .query('emailTracking')
      .withIndex('by_project', (q) => q.eq('projectId', projectId))
      .collect();

    console.log(`📧 Found ${emailRecords.length} email tracking records for project`);

    let updatedCount = 0;

    // Update each email record to mark project as accessed
    for (const record of emailRecords) {
      if (!record.projectAccessedAt) {
        await ctx.db.patch(record._id, {
          projectAccessedAt: now,
          projectAccessCount: (record.projectAccessCount || 0) + 1,
        });

        if (record.emailId) {
          console.log(`✅ Marked email ${record.emailId} as opened via project access`);
        }
        updatedCount++;
      } else {
        // Increment access count for repeat visits
        await ctx.db.patch(record._id, {
          projectAccessCount: (record.projectAccessCount || 0) + 1,
        });
        updatedCount++;
      }
    }

    console.log(`📊 Updated ${updatedCount} email tracking records for project access`);
    return { updatedRecords: updatedCount };
  }
});
