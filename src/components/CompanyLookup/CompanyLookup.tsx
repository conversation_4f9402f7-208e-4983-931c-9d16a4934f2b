import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { useDebouncedCompanyLookup } from '../../hooks/useCompanyLookup';
import { CompanyInfo, formatNorwegianDate, formatOrganizationForm, formatIndustryInfo } from '../../services/companyLookup';
import { TextInput } from '../ui';

export interface CompanyLookupProps {
  /** Current company name value */
  companyName: string;
  /** Callback when company name changes */
  onCompanyNameChange: (name: string) => void;
  /** Callback when company is selected and should auto-fill form */
  onCompanySelect: (company: CompanyInfo) => void;
  /** Whether the lookup is disabled */
  disabled?: boolean;
  /** Error message for the company name field */
  error?: string;
  /** Additional CSS classes */
  className?: string;
  /** Organization number of contractor's own company to prevent selection */
  contractorOrgNumber?: string;
}

export interface CompanyLookupRef {
  /** Reset all component state */
  reset: () => void;
}

/**
 * Company lookup component with search-as-you-type functionality
 * Integrates with Brønnøysundregisteret for Norwegian company data
 * 
 * @example
 * ```tsx
 * <CompanyLookup
 *   companyName={formData.customerName}
 *   onCompanyNameChange={(name) => updateFormData({ customerName: name })}
 *   onCompanySelect={(company) => {
 *     updateFormData({
 *       customerName: company.name,
 *       orgNumber: company.organizationNumber,
 *       streetAddress: company.visitingAddress?.street,
 *       postalCode: company.visitingAddress?.postalCode,
 *       city: company.visitingAddress?.city
 *     });
 *   }}
 *   error={errors.customerName}
 * />
 * ```
 */
export const CompanyLookup = forwardRef<CompanyLookupRef, CompanyLookupProps>(({
  companyName,
  onCompanyNameChange,
  onCompanySelect,
  disabled = false,
  error,
  className = '',
  contractorOrgNumber
}, ref) => {
  const [showResults, setShowResults] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const {
    results,
    isLoading,
    error: lookupError,
    hasSearched,
    selectedCompany,
    debouncedSearchByName,
    selectCompany,
    clearResults,
    clearError
  } = useDebouncedCompanyLookup(500);

  // Helper function to check if a company is the contractor's own company
  const isContractorOwnCompany = (company: CompanyInfo): boolean => {
    return !!(contractorOrgNumber && company.organizationNumber === contractorOrgNumber);
  };

  // Sync search query with company name when externally changed
  useEffect(() => {
    setSearchQuery(companyName);
  }, [companyName]);

  // Complete reset function
  const handleCompleteReset = () => {
    setSearchQuery('');
    setShowResults(false);
    clearResults();
    clearError();
    onCompanyNameChange('');
  };

  // Expose reset function to parent component via ref
  useImperativeHandle(ref, () => ({
    reset: handleCompleteReset
  }), []);

  // Handle company name input change
  const handleCompanyNameChange = (value: string) => {
    setSearchQuery(value);
    onCompanyNameChange(value);

    if (value.length >= 2) {
      debouncedSearchByName(value);
      setShowResults(true);
      clearError();
    } else {
      setShowResults(false);
      clearResults();
    }
  };

  // Handle company selection from results
  const handleCompanySelect = (company: CompanyInfo) => {
    // Prevent selection of contractor's own company
    if (isContractorOwnCompany(company)) {
      // Don't select the company, just hide results
      setShowResults(false);
      return;
    }

    selectCompany(company);
    onCompanySelect(company);
    setShowResults(false);
    clearError();
  };



  // Close results when clicking outside
  const handleBlur = () => {
    // Delay hiding results to allow for clicks on result items
    setTimeout(() => {
      setShowResults(false);
    }, 200);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Company Name Input with Search */}
      <div className="space-y-2">
        <TextInput
          label="Bedriftsnavn"
          placeholder="F.eks. Equinor ASA"
          required
          fullWidth
          value={companyName}
          onChange={(e) => handleCompanyNameChange(e.target.value)}
          onBlur={handleBlur}
          onFocus={() => {
            if (results.length > 0 && hasSearched) {
              setShowResults(true);
            }
          }}
          error={error}
          disabled={disabled}
          helperText="Start å skrive for å søke i Brønnøysundregisteret"
          autoComplete="off"
          data-form-type="other"
        />

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex items-center gap-2 text-sm text-jobblogg-text-muted">
            <div className="w-4 h-4 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin" />
            <span>Søker etter bedrift...</span>
          </div>
        )}

        {/* Error Message */}
        {lookupError && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-lg p-3">
            <div className="flex items-start gap-2">
              <svg className="w-5 h-5 text-jobblogg-error flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-jobblogg-error">
                  {lookupError.message}
                </p>
                {lookupError.details && (
                  <p className="text-xs text-jobblogg-error mt-1 opacity-75">
                    {lookupError.details}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && (hasSearched || isLoading) && (
        <div
          className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-jobblogg-border rounded-lg shadow-lg max-h-64 overflow-y-auto"
          role="listbox"
          aria-label="Søkeresultater for bedrift"
        >
          {isLoading ? (
            <div className="px-3 py-4 text-center text-sm text-jobblogg-text-muted">
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin" />
                <span>Søker etter bedrift...</span>
              </div>
            </div>
          ) : results.length > 0 ? (
            <div className="py-2">
              <div className="px-3 py-2 text-xs font-medium text-jobblogg-text-muted border-b border-jobblogg-border">
                Fant {results.length} bedrift{results.length !== 1 ? 'er' : ''}
              </div>
              {results.map((company, index) => {
                const isOwnCompany = isContractorOwnCompany(company);
                return (
                  <button
                    key={`${company.organizationNumber}-${index}`}
                    onClick={() => handleCompanySelect(company)}
                    className={`w-full text-left px-3 py-3 border-b border-jobblogg-border last:border-b-0 focus:outline-none ${
                      isOwnCompany
                        ? 'bg-jobblogg-neutral-secondary cursor-not-allowed opacity-75'
                        : 'hover:bg-jobblogg-neutral transition-colors duration-150 focus:ring-2 focus:ring-jobblogg-primary focus:ring-inset'
                    }`}
                    role="option"
                    aria-selected="false"
                    tabIndex={isOwnCompany ? -1 : 0}
                    disabled={isOwnCompany}
                  >
                  <div className="space-y-1">
                    <div className="font-medium text-jobblogg-text-strong flex items-center gap-2">
                      <span>{company.name}</span>
                      {isOwnCompany && (
                        <span className="px-2 py-0.5 bg-jobblogg-warning-soft text-jobblogg-warning text-xs rounded font-medium">
                          Din bedrift
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-jobblogg-text-muted">
                      Org.nr: {company.organizationNumber}
                      {/* Organization Form */}
                      {company.organizationForm && (
                        <span className="ml-2 px-2 py-0.5 bg-jobblogg-primary-soft text-jobblogg-primary text-xs rounded">
                          {formatOrganizationForm(company.organizationFormCode, company.organizationForm)}
                        </span>
                      )}
                    </div>

                    <div className="text-sm text-jobblogg-text-muted space-y-1">
                      {/* Address */}
                      {(() => {
                        const address = company.visitingAddress || company.businessAddress;
                        if (address && (address.street || address.postalCode || address.city)) {
                          return (
                            <div>
                              {address.street && (
                                <>
                                  {address.street}
                                  {address.postalCode && address.city && (
                                    <>, {address.postalCode} {address.city}</>
                                  )}
                                </>
                              )}
                              {!address.street && address.postalCode && address.city && (
                                <>{address.postalCode} {address.city}</>
                              )}
                            </div>
                          );
                        }
                        return <div>Ingen adresse registrert</div>;
                      })()}

                      {/* Industry Information */}
                      {(company.naeringskode1 || company.industryDescription) && (
                        <div className="text-xs">
                          <span className="font-medium">Bransje:</span> {formatIndustryInfo(company.naeringskode1, company.industryDescription)}
                        </div>
                      )}

                      {/* Establishment Date */}
                      {company.establishmentDate && (
                        <div className="text-xs">
                          <span className="font-medium">Stiftet:</span> {formatNorwegianDate(company.establishmentDate)}
                        </div>
                      )}

                      {/* Number of Employees */}
                      {company.numberOfEmployees && (
                        <div className="text-xs">
                          <span className="font-medium">Ansatte:</span> {company.numberOfEmployees}
                        </div>
                      )}

                      {/* Managing Director */}
                      {company.managingDirector && (
                        <div className="text-xs">
                          <span className="font-medium">Daglig leder:</span> {company.managingDirector.fullName}
                        </div>
                      )}
                    </div>

                    {/* Warning message for contractor's own company */}
                    {isOwnCompany && (
                      <div className="mt-2 p-2 bg-jobblogg-warning-soft border border-jobblogg-warning-border rounded text-xs text-jobblogg-warning">
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <span className="font-medium">Du kan ikke velge din egen bedrift som kunde</span>
                        </div>
                      </div>
                    )}

                    {company.status !== 'active' && (
                      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-warning-soft text-jobblogg-warning">
                        {company.status === 'dissolved' ? 'Oppløst' : 'Inaktiv'}
                      </div>
                    )}
                  </div>
                </button>
                );
              })}
            </div>
          ) : (
            <div className="px-3 py-4 text-center text-sm text-jobblogg-text-muted">
              Ingen bedrifter funnet for "{searchQuery}"
              <div className="mt-2 text-xs">
                Prøv et annet søkeord eller skriv inn informasjonen manuelt
              </div>
            </div>
          )}
        </div>
      )}

      {/* Selected Company Confirmation */}
      {selectedCompany && !showResults && (
        <div className="mt-3 bg-jobblogg-success-soft border border-jobblogg-success rounded-lg p-3">
          <div className="flex items-start gap-2">
            <svg className="w-5 h-5 text-jobblogg-success flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="flex-1">
              <p className="text-sm font-medium text-jobblogg-success">
                Bedriftsinformasjon hentet fra Brønnøysundregisteret
              </p>
              <p className="text-xs text-jobblogg-success mt-1 opacity-75">
                Adresse og organisasjonsnummer er automatisk utfylt
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

CompanyLookup.displayName = 'CompanyLookup';
