import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { TextMuted } from '../ui';

interface NotificationBellProps {
  className?: string;
}

export const NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {
  const { user } = useUser();
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState(false);

  // Query invitation notification statistics (only incoming invitations)
  const invitationStats = useQuery(
    api.subcontractorInvitations.getInvitationNotificationStats,
    user?.id ? { userId: user.id } : "skip"
  );

  // Query recent pending incoming invitations only (not outgoing invitations sent by this user)
  const pendingInvitations = useQuery(
    api.subcontractorInvitations.getIncomingSubcontractorInvitations,
    user?.id ? {
      userId: user.id,
      status: 'pending',
    } : "skip"
  );

  // Query unread response notifications (when subcontractors respond to contractor's invitations)
  const responseNotifications = useQuery(
    api.subcontractorInvitations.getInvitationResponseNotifications,
    user?.id ? {
      userId: user.id,
      status: 'unread',
    } : "skip"
  );

  const pendingInvitationCount = invitationStats?.pendingCount || 0;
  const responseNotificationCount = responseNotifications?.length || 0;
  const totalNotificationCount = pendingInvitationCount + responseNotificationCount;

  const recentInvitations = pendingInvitations?.slice(0, 2) || [];
  const recentResponses = responseNotifications?.slice(0, 2) || [];

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short'
    });
  };

  const handleBellClick = () => {
    if (totalNotificationCount > 0) {
      setShowDropdown(!showDropdown);
    } else {
      navigate('/invitations');
    }
  };

  const handleViewAll = () => {
    setShowDropdown(false);
    navigate('/invitations');
  };

  const handleInvitationClick = (invitationId: string) => {
    setShowDropdown(false);
    navigate(`/invitations/${invitationId}`);
  };

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-notification-bell]')) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showDropdown]);

  return (
    <div className={`relative ${className}`} data-notification-bell>
      {/* Bell Button */}
      <button
        onClick={handleBellClick}
        className={`
          relative p-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
          ${totalNotificationCount > 0
            ? 'text-jobblogg-warning hover:bg-jobblogg-warning/10'
            : 'text-jobblogg-text-medium hover:bg-jobblogg-neutral hover:text-jobblogg-text-strong'
          }
        `}
        aria-label={`Notifikasjoner${totalNotificationCount > 0 ? ` (${totalNotificationCount} nye)` : ''}`}
      >
        <svg
          className={`w-6 h-6 ${totalNotificationCount > 0 ? 'animate-pulse' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>

        {/* Notification Badge */}
        {totalNotificationCount > 0 && (
          <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-jobblogg-error rounded-full min-w-[20px] h-5 animate-bounce">
            {totalNotificationCount > 9 ? '9+' : totalNotificationCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {showDropdown && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-lg border border-jobblogg-border z-50 animate-slide-down">
          {/* Header */}
          <div className="px-4 py-3 border-b border-jobblogg-border">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-jobblogg-text-strong">
                Notifikasjoner
              </h3>
              <span className="text-xs text-jobblogg-text-medium">
                {totalNotificationCount} nye
              </span>
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {(recentInvitations.length > 0 || recentResponses.length > 0) ? (
              <div className="py-2">
                {/* Response Notifications (shown first as they're more urgent) */}
                {recentResponses.map((response) => (
                  <button
                    key={response._id}
                    onClick={() => {
                      if (response.projectInfo) {
                        navigate(`/project/${response.projectInfo._id}`);
                        setShowDropdown(false);
                      }
                    }}
                    className="w-full px-4 py-3 text-left hover:bg-jobblogg-neutral/50 transition-colors duration-200 border-b border-jobblogg-border/50 last:border-b-0"
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                        response.isAcceptance ? 'bg-jobblogg-success animate-pulse' : 'bg-jobblogg-error animate-pulse'
                      }`}></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-jobblogg-text-strong truncate">
                          {response.isAcceptance ? '✅ Invitasjon godtatt' : '❌ Invitasjon avslått'}
                        </p>
                        <p className="text-xs text-jobblogg-text-medium truncate">
                          {response.data?.subcontractorName} • {response.data?.projectName}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-jobblogg-text-medium">
                            {formatDate(response.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                ))}

                {/* Invitation Notifications */}
                {recentInvitations.map((invitation) => (
                  <button
                    key={invitation._id}
                    onClick={() => handleInvitationClick(invitation._id)}
                    className="w-full px-4 py-3 text-left hover:bg-jobblogg-neutral/50 transition-colors duration-200 border-b border-jobblogg-border/50 last:border-b-0"
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-jobblogg-warning rounded-full mt-2 flex-shrink-0 animate-pulse"></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-jobblogg-text-strong truncate">
                          📨 Ny invitasjon
                        </p>
                        <p className="text-xs text-jobblogg-text-medium truncate">
                          {invitation.projectPreview?.name} • {invitation.projectPreview?.mainContractorCompany}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-jobblogg-accent">
                            {invitation.subcontractorSpecialization}
                          </span>
                          <span className="text-xs text-jobblogg-text-medium">
                            • {formatDate(invitation.invitedAt || invitation._creationTime)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="px-4 py-6 text-center">
                <svg className="w-8 h-8 text-jobblogg-text-medium mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <p className="text-sm text-jobblogg-text-medium">
                  Ingen nye notifikasjoner
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 py-3 border-t border-jobblogg-border">
            <button
              onClick={handleViewAll}
              className="w-full text-sm text-jobblogg-primary hover:text-jobblogg-primary-hover font-medium transition-colors duration-200"
            >
              Se alle invitasjoner
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
