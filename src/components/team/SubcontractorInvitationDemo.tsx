import React, { useState } from 'react';
import { SubcontractorSearchModal } from './SubcontractorSearchModal';
import { RegistrationInvitationStatus } from './RegistrationInvitationStatus';
import { PrimaryButton, SecondaryButton, Heading2, BodyText, TextMuted } from '../ui';

interface InvitationStatus {
  id: string;
  companyName: string;
  email: string;
  status: 'sending' | 'sent' | 'registered' | 'expired' | 'error';
  sentAt: Date;
  expiresAt: Date;
}

/**
 * Demo component showing the improved subcontractor invitation UX
 * This demonstrates the flow from search → registration invitation → adding to project
 */
export const SubcontractorInvitationDemo: React.FC = () => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [invitations, setInvitations] = useState<InvitationStatus[]>([
    {
      id: '1',
      companyName: 'Hansen <PERSON>ør <PERSON>',
      email: '<EMAIL>',
      status: 'sent',
      sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    },
    {
      id: '2',
      companyName: 'Nordahl Elektro',
      email: '<EMAIL>',
      status: 'registered',
      sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      expiresAt: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000), // 4 days from now
    },
    {
      id: '3',
      companyName: 'Gamle Invitasjon AS',
      email: '<EMAIL>',
      status: 'expired',
      sentAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
      expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    },
  ]);

  const handleSubcontractorSelect = (company: any, user: any) => {
    console.log('Selected subcontractor:', { company, user });
    alert(`Valgt: ${user.displayName} fra ${company.name}`);
    setIsSearchModalOpen(false);
  };

  const handleSendRegistrationInvitation = (companyName: string, email: string) => {
    const newInvitation: InvitationStatus = {
      id: Date.now().toString(),
      companyName,
      email,
      status: 'sending',
      sentAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    };

    setInvitations(prev => [newInvitation, ...prev]);

    // Simulate sending process
    setTimeout(() => {
      setInvitations(prev => 
        prev.map(inv => 
          inv.id === newInvitation.id 
            ? { ...inv, status: 'sent' as const }
            : inv
        )
      );
    }, 2000);
  };

  const handleResendInvitation = (invitationId: string) => {
    setInvitations(prev =>
      prev.map(inv =>
        inv.id === invitationId
          ? { ...inv, status: 'sending', sentAt: new Date(), expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) }
          : inv
      )
    );

    // Simulate sending process
    setTimeout(() => {
      setInvitations(prev =>
        prev.map(inv =>
          inv.id === invitationId && inv.status === 'sending'
            ? { ...inv, status: 'sent' }
            : inv
        )
      );
    }, 2000);
  };

  const handleAddToProject = (invitationId: string) => {
    const invitation = invitations.find(inv => inv.id === invitationId);
    if (invitation) {
      alert(`${invitation.companyName} er nå lagt til i prosjektet!`);
      // Remove from invitations list since they're now in the project
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <Heading2>Forbedret Underleverandør-invitasjon UX</Heading2>
        <BodyText className="text-jobblogg-text-medium">
          Demonstrasjon av den nye brukeropplevelsen for å invitere underleverandører
        </BodyText>
      </div>

      {/* Action Button */}
      <div className="text-center">
        <PrimaryButton onClick={() => setIsSearchModalOpen(true)}>
          🔍 Søk etter underleverandør
        </PrimaryButton>
      </div>

      {/* Pending Registration Invitations */}
      {invitations.length > 0 && (
        <div className="space-y-4">
          <Heading2 className="text-xl">Registreringsinvitasjoner</Heading2>
          <div className="space-y-3">
            {invitations.map((invitation) => (
              <RegistrationInvitationStatus
                key={invitation.id}
                companyName={invitation.companyName}
                email={invitation.email}
                status={invitation.status}
                sentAt={invitation.sentAt}
                expiresAt={invitation.expiresAt}
                onResend={() => handleResendInvitation(invitation.id)}
                onAddToProject={() => handleAddToProject(invitation.id)}
              />
            ))}
          </div>
        </div>
      )}

      {/* UX Explanation */}
      <div className="bg-jobblogg-card-bg rounded-xl p-6 space-y-4">
        <Heading2 className="text-lg">Hvordan den nye UX-en fungerer:</Heading2>
        <div className="space-y-3 text-sm">
          <div className="flex items-start gap-3">
            <span className="text-jobblogg-primary font-bold">1.</span>
            <div>
              <BodyText className="font-medium">Smart søk med forklaring</BodyText>
              <TextMuted>Brukeren får tydelig beskjed om at underleverandøren må være registrert først</TextMuted>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <span className="text-jobblogg-primary font-bold">2.</span>
            <div>
              <BodyText className="font-medium">Automatisk fallback til registreringsinvitasjon</BodyText>
              <TextMuted>Hvis søket ikke gir resultater, tilbys registreringsinvitasjon direkte</TextMuted>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <span className="text-jobblogg-primary font-bold">3.</span>
            <div>
              <BodyText className="font-medium">Status-tracking</BodyText>
              <TextMuted>Brukeren ser status på alle sendte invitasjoner og kan handle når registrering er fullført</TextMuted>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <span className="text-jobblogg-primary font-bold">4.</span>
            <div>
              <BodyText className="font-medium">Sømløs overgang</BodyText>
              <TextMuted>Når underleverandøren er registrert, kan de legges til prosjektet med ett klikk</TextMuted>
            </div>
          </div>
        </div>
      </div>

      {/* Search Modal */}
      <SubcontractorSearchModal
        isOpen={isSearchModalOpen}
        onClose={() => setIsSearchModalOpen(false)}
        onSelect={handleSubcontractorSelect}
        currentProjectId="demo-project"
      />
    </div>
  );
};
