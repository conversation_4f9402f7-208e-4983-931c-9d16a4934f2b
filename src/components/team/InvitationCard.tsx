import React, { useState } from 'react';
import { BodyText, TextMuted, SecondaryButton, DangerButton } from '../ui';

interface Invitation {
  _id: string;
  role: string;
  invitedBy: string;
  invitedAt?: number;
  expiresAt: number;
  isExpired: boolean;
  invitationToken: string;
}

interface InvitationCardProps {
  invitation: Invitation;
  onRevoke: () => void;
  onResend: () => void;
}

export const InvitationCard: React.FC<InvitationCardProps> = ({
  invitation,
  onRevoke,
  onResend,
}) => {
  const [isRevoking, setIsRevoking] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const roleLabel = invitation.role === 'administrator' ? 'Administrator' : 'Utførende';
  
  // Format dates
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDateShort = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
    });
  };

  // Calculate time until expiration
  const getExpirationStatus = () => {
    const now = Date.now();
    const timeUntilExpiration = invitation.expiresAt - now;
    
    if (timeUntilExpiration <= 0) {
      return { text: 'Utløpt', color: 'text-jobblogg-error', urgent: true };
    }
    
    const daysUntilExpiration = Math.ceil(timeUntilExpiration / (24 * 60 * 60 * 1000));
    
    if (daysUntilExpiration <= 1) {
      return { text: 'Utløper i dag', color: 'text-jobblogg-warning', urgent: true };
    } else if (daysUntilExpiration <= 2) {
      return { text: `Utløper om ${daysUntilExpiration} dager`, color: 'text-jobblogg-warning', urgent: false };
    } else {
      return { text: `Utløper ${formatDateShort(invitation.expiresAt)}`, color: 'text-jobblogg-text-medium', urgent: false };
    }
  };

  const expirationStatus = getExpirationStatus();

  const handleRevoke = async () => {
    setIsRevoking(true);
    try {
      await onRevoke();
    } finally {
      setIsRevoking(false);
    }
  };

  const handleResend = async () => {
    setIsResending(true);
    try {
      await onResend();
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className={`bg-white rounded-xl border shadow-soft p-6 transition-all duration-200 ${
      invitation.isExpired 
        ? 'border-jobblogg-error/30 bg-jobblogg-error/5' 
        : expirationStatus.urgent
        ? 'border-jobblogg-warning/30 bg-jobblogg-warning/5'
        : 'border-jobblogg-border hover:shadow-medium'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4 flex-1">
          {/* Invitation icon */}
          <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${
            invitation.isExpired
              ? 'bg-jobblogg-error/10'
              : 'bg-jobblogg-warning/10'
          }`}>
            <svg className={`w-6 h-6 ${
              invitation.isExpired ? 'text-jobblogg-error' : 'text-jobblogg-warning'
            }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <BodyText className="font-medium">
                Invitasjon til {roleLabel}
              </BodyText>
              
              {/* Status badge */}
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                invitation.isExpired
                  ? 'bg-jobblogg-error/10 text-jobblogg-error border border-jobblogg-error/20'
                  : 'bg-jobblogg-warning/10 text-jobblogg-warning border border-jobblogg-warning/20'
              }`}>
                {invitation.isExpired ? 'Utløpt' : 'Ventende'}
              </span>
            </div>

            <div className="space-y-1">
              {invitation.invitedAt && (
                <TextMuted className="text-sm">
                  Sendt {formatDate(invitation.invitedAt)}
                </TextMuted>
              )}
              
              <TextMuted className={`text-sm font-medium ${expirationStatus.color}`}>
                {expirationStatus.text}
              </TextMuted>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 ml-4">
          {!invitation.isExpired && (
            <SecondaryButton
              size="sm"
              onClick={handleResend}
              disabled={isResending}
              icon={isResending ? (
                <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )}
            >
              {isResending ? 'Sender...' : 'Send på nytt'}
            </SecondaryButton>
          )}
          
          <DangerButton
            size="sm"
            variant="outline"
            onClick={handleRevoke}
            disabled={isRevoking}
            icon={isRevoking ? (
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            )}
          >
            {isRevoking ? 'Trekker tilbake...' : 'Trekk tilbake'}
          </DangerButton>
        </div>
      </div>

      {/* Invitation token (for development/testing) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 pt-4 border-t border-jobblogg-border/50">
          <TextMuted className="text-xs font-mono break-all">
            Token: {invitation.invitationToken}
          </TextMuted>
        </div>
      )}
    </div>
  );
};
