import React, { useState } from 'react';
import { useAction, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  TextInput,
  SelectInput,
  FormError,
  Heading2,
  BodyText,
  PhoneInput
} from '../ui';

interface InviteTeamMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const InviteTeamMemberModal: React.FC<InviteTeamMemberModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { user } = useUser();
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [role, setRole] = useState<'administrator' | 'utfoerende'>('utfoerende');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [invitationResult, setInvitationResult] = useState<any>(null);

  const inviteTeamMember = useAction(api.teamManagement.inviteTeamMemberMagicLink);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError('Du må være logget inn for å invitere teammedlemmer');
      return;
    }

    // Validate required fields
    if (!email.trim()) {
      setError('E-postadresse er påkrevd');
      return;
    }

    if (!firstName.trim()) {
      setError('Fornavn er påkrevd');
      return;
    }

    if (!lastName.trim()) {
      setError('Etternavn er påkrevd');
      return;
    }

    if (!phone.trim()) {
      setError('Mobilnummer er påkrevd');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      setError('Ugyldig e-postadresse');
      return;
    }

    // Phone validation (Norwegian format - raw digits)
    const phoneRegex = /^\d{8}$/; // PhoneInput sends raw 8-digit numbers
    if (!phoneRegex.test(phone)) {
      const errorMessage = phone.length < 8
        ? `Mobilnummer må være 8 siffer (du har skrevet ${phone.length} siffer)`
        : 'Mobilnummer må være 8 siffer';

      setError(errorMessage);
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Format phone number for backend (add +47 prefix and spaces)
      const formattedPhone = `+47 ${phone.slice(0, 3)} ${phone.slice(3, 5)} ${phone.slice(5)}`;

      const result = await inviteTeamMember({
        email: email.trim(),
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        phone: formattedPhone,
        role,
        invitedBy: user.id,
      });

      setInvitationResult({
        success: true,
        email: email,
        firstName: firstName,
        lastName: lastName,
        message: 'Magic link invitasjon sendt via e-post!',
        invitationLink: result.invitationLink,
        invitationToken: result.invitationToken,
        expiresAt: result.expiresAt,
      });

    } catch (error: any) {
      setError(error.message || 'Kunne ikke opprette invitasjon');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setEmail('');
      setFirstName('');
      setLastName('');
      setPhone('');
      setRole('utfoerende');
      setError('');
      setInvitationResult(null);
      onClose();
    }
  };

  const handleFinishInvitation = () => {
    // Reset form and close modal
    setEmail('');
    setRole('utfoerende');
    setError('');
    setInvitationResult(null);
    onSuccess();
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <div className="p-6">
        {!invitationResult ? (
          // Show invitation form
          <>
            <div className="mb-6">
              <Heading2 className="mb-2">Inviter teammedlem</Heading2>
              <BodyText className="text-jobblogg-text-medium">
                Send en magic link invitasjon til en kollega. De kan bli med i teamet med bare ett klikk.
              </BodyText>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-jobblogg-text-strong">
                  E-postadresse <span className="text-jobblogg-error">*</span>
                </label>
                <TextInput
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  disabled={isSubmitting}
                  required
                />
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="block text-sm font-medium text-jobblogg-text-strong">
                    Fornavn <span className="text-jobblogg-error">*</span>
                  </label>
                  <TextInput
                    id="firstName"
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="Ola"
                    disabled={isSubmitting}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="lastName" className="block text-sm font-medium text-jobblogg-text-strong">
                    Etternavn <span className="text-jobblogg-error">*</span>
                  </label>
                  <TextInput
                    id="lastName"
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Nordmann"
                    disabled={isSubmitting}
                    required
                  />
                </div>
              </div>

              {/* Phone Field */}
              <div className="space-y-2">
                <label htmlFor="phone" className="block text-sm font-medium text-jobblogg-text-strong">
                  Mobilnummer <span className="text-jobblogg-error">*</span>
                </label>
                <PhoneInput
                  id="phone"
                  value={phone}
                  onChange={setPhone}
                  placeholder="XXX XX XXX"
                  disabled={isSubmitting}
                  required
                />
              </div>

              {/* Role Field */}
              <div className="space-y-2">
                <label htmlFor="role" className="block text-sm font-medium text-jobblogg-text-strong">
                  Rolle <span className="text-jobblogg-error">*</span>
                </label>
                <SelectInput
                  id="role"
                  value={role}
                  onChange={(e) => setRole(e.target.value as 'administrator' | 'utfoerende')}
                  disabled={isSubmitting}
                  required
                  options={[
                    { value: 'utfoerende', label: 'Utførende' },
                    { value: 'administrator', label: 'Administrator' }
                  ]}
                />
                <BodyText className="text-sm text-jobblogg-text-medium mt-1">
                  {role === 'administrator'
                    ? 'Administrator kan invitere andre brukere og administrere teamet.'
                    : 'Utførende kan opprette og administrere prosjekter, men ikke invitere andre brukere.'
                  }
                </BodyText>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4">
                  <FormError>{error}</FormError>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
                <SecondaryButton
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Avbryt
                </SecondaryButton>

                <PrimaryButton
                  type="submit"
                  disabled={isSubmitting || !email.trim()}
                  icon={isSubmitting ? (
                    <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                >
                  {isSubmitting ? 'Sender magic link...' : 'Send magic link invitasjon'}
                </PrimaryButton>
              </div>
            </form>
          </>
        ) : (
          // Show invitation result with link
          <>
            <div className="mb-6">
              <Heading2 className="mb-2">Magic link sendt!</Heading2>
              <BodyText className="text-jobblogg-text-medium">
                <strong>{invitationResult.firstName} {invitationResult.lastName}</strong> ({invitationResult.email}) har mottatt en magic link invitasjon til teamet.
              </BodyText>
            </div>

            {/* Success message for email sent */}
            <div className="mb-6 p-4 bg-jobblogg-success-soft border border-jobblogg-success rounded-lg">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-jobblogg-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <BodyText className="text-jobblogg-success font-medium mb-1">
                    E-post sendt automatisk
                  </BodyText>
                  <BodyText className="text-sm text-jobblogg-text-medium">
                    Invitasjonslink og instruksjoner er sendt. Ingen ytterligere handling kreves.
                  </BodyText>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
              <PrimaryButton
                type="button"
                onClick={handleFinishInvitation}
              >
                Ferdig
              </PrimaryButton>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};
