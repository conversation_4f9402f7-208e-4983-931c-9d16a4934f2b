import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  noIndex?: boolean;
}

/**
 * SEO Head component for managing meta tags and structured data
 * 
 * @example
 * ```tsx
 * <SEOHead 
 *   title="JobbLogg - Dokumenter arbeidet ditt enkelt og profesjonelt"
 *   description="Transparent prosjektoppfølging for håndverkere og fagfolk"
 *   keywords="håndverker, prosjektdokumentasjon, byggebransje, chat"
 * />
 * ```
 */
export const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'JobbLogg - Dokumenter arbeidet ditt enkelt og profesjonelt',
  description = 'Transparent prosjektoppfølging for håndverkere og fagfolk. Del framgang med kunder i sanntid og bygg tillit gjennom åpenhet.',
  keywords = 'hånd<PERSON><PERSON>, prosjektdokumentasjon, byggebransje, chat, transparent, dokumentasjon, fagfolk, kunde, kommunikasjon',
  image = '/og-image.jpg',
  url = 'https://jobblogg.no',
  type = 'website',
  noIndex = false,
}) => {
  const fullTitle = title.includes('JobbLogg') ? title : `${title} | JobbLogg`;
  const fullUrl = url.startsWith('http') ? url : `https://jobblogg.no${url}`;
  const fullImage = image.startsWith('http') ? image : `https://jobblogg.no${image}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:site_name" content="JobbLogg" />
      <meta property="og:locale" content="nb_NO" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="JobbLogg" />
      <meta name="language" content="Norwegian" />
      <meta name="geo.region" content="NO" />
      <meta name="geo.country" content="Norway" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Structured Data - Organization */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "JobbLogg",
          "description": description,
          "url": "https://jobblogg.no",
          "logo": "https://jobblogg.no/logo.png",
          "contactPoint": {
            "@type": "ContactPoint",
            "email": "<EMAIL>",
            "contactType": "customer service",
            "areaServed": "NO",
            "availableLanguage": "Norwegian"
          },
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "NO"
          },
          "sameAs": [
            // Add social media links when available
          ]
        })}
      </script>
      
      {/* Structured Data - WebApplication */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "JobbLogg",
          "description": description,
          "url": "https://jobblogg.no",
          "applicationCategory": "BusinessApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "NOK",
            "description": "Gratis prøveperiode"
          },
          "featureList": [
            "Prosjektdokumentasjon",
            "Sanntids-chat",
            "Enkel deling",
            "Sikker lagring",
            "Team-samarbeid",
            "Rapporter og innsikt"
          ],
          "screenshot": fullImage,
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "150",
            "bestRating": "5",
            "worstRating": "1"
          }
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
