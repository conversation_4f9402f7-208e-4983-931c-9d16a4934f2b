import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { PrimaryButton, TextArea } from './ui';

interface WithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  projectName: string;
  userId: string;
  onWithdrawalComplete: () => void;
}

export const WithdrawalModal: React.FC<WithdrawalModalProps> = ({
  isOpen,
  onClose,
  projectId,
  projectName,
  userId,
  onWithdrawalComplete
}) => {
  const [withdrawalReason, setWithdrawalReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const withdrawFromProject = useMutation(api.teamManagement.withdrawFromSubcontractorAssignment);

  const handleWithdraw = async () => {
    if (!projectId || !userId) return;

    setIsSubmitting(true);
    try {
      await withdrawFromProject({
        projectId: projectId as any,
        userId,
        withdrawalReason: withdrawalReason.trim() || undefined
      });

      // Success - close modal and navigate
      onClose();
      onWithdrawalComplete();
    } catch (error) {
      console.error('Withdrawal failed:', error);
      // TODO: Show error toast
      alert('Kunne ikke trekke seg fra prosjektet. Prøv igjen senere.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setWithdrawalReason('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Trekk deg fra prosjekt
            </h2>
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              aria-label="Lukk"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="space-y-4 mb-6">
            <div className="bg-jobblogg-warning-soft border border-jobblogg-warning/20 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-jobblogg-warning mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <h3 className="font-medium text-jobblogg-text-strong mb-1">
                    Viktig informasjon
                  </h3>
                  <p className="text-sm text-jobblogg-text-medium">
                    Ved å trekke deg fra prosjektet "{projectName}" vil:
                  </p>
                  <ul className="text-sm text-jobblogg-text-medium mt-2 space-y-1">
                    <li>• Du og alle teammedlemmer fra ditt firma fjernes fra prosjektet</li>
                    <li>• Dere mister tilgang til prosjektdata og samtaler</li>
                    <li>• Handlingen kan ikke angres automatisk</li>
                    <li>• Hovedkontraktøren kan tildele dere på nytt senere hvis ønskelig</li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="withdrawal-reason" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                Grunn for å trekke seg (valgfritt)
              </label>
              <TextArea
                id="withdrawal-reason"
                value={withdrawalReason}
                onChange={(e) => setWithdrawalReason(e.target.value)}
                placeholder="F.eks. 'Ikke kapasitet', 'Endret prioriteringer', 'Tekniske utfordringer'..."
                rows={3}
                disabled={isSubmitting}
                className="w-full"
              />
              <p className="text-xs text-jobblogg-text-muted mt-1">
                Denne informasjonen vil være synlig for hovedkontraktøren
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 text-sm font-medium text-jobblogg-text-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 min-h-[44px]"
            >
              Avbryt
            </button>
            <PrimaryButton
              onClick={handleWithdraw}
              disabled={isSubmitting}
              loading={isSubmitting}
              loadingText="Trekker seg..."
              variant="danger"
              className="flex-1 min-h-[44px]"
            >
              Trekk deg fra prosjektet
            </PrimaryButton>
          </div>
        </div>
      </div>
    </div>
  );
};
