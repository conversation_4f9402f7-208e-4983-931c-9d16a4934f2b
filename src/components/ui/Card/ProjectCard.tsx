import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { ConfirmDialog } from '../Dialog/ConfirmDialog';
import { formatAddress, isAddressComplete } from '../../../utils/googleMaps';
import { EmailStatusIndicator } from '../../EmailTracking/EmailStatusIndicator';

interface ProjectCardProps {
  /** Project title */
  title: string;
  /** Project description */
  description: string;
  /** Project ID for navigation */
  projectId: string;
  /** Project owner user ID */
  projectOwnerId?: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Click handler for card interaction */
  onClick: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Animation delay for staggered animations */
  animationDelay?: string;
  /** Customer information */
  customer?: {
    name: string;
    type: 'privat' | 'firma';
    // Legacy address field for backward compatibility
    address?: string;
    // New structured address fields
    streetAddress?: string;
    postalCode?: string;
    city?: string;
    entrance?: string;
    contactPerson?: string;
  } | null;
  /** Archive status */
  isArchived?: boolean;
  /** Archive timestamp */
  archivedAt?: number;
  /** Show archive actions */
  showArchiveActions?: boolean;
  /** Archive action callbacks */
  onArchive?: () => void;
  onRestore?: () => void;
}

/**
 * Project card component following JobbLogg design system
 * Displays project information with gradient background and hover effects
 * 
 * @example
 * ```tsx
 * <ProjectCard
 *   title="Kjøkkenrenovering"
 *   description="Komplett renovering av kjøkken med nye skap og benkeplate"
 *   projectId="project-123"
 *   updatedAt="2025-01-26"
 *   onClick={() => navigate(`/project/project-123`)}
 * />
 * ```
 */
export const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  description,
  projectId: _projectId,
  projectOwnerId,
  updatedAt,
  onClick,
  className = '',
  animationDelay = '0s',
  customer,
  isArchived = false,
  archivedAt,
  showArchiveActions = false,
  onArchive,
  onRestore,
}) => {
  const { user } = useUser();
  // State for confirmation dialogs
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  const handleClick = () => {
    onClick();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick();
    }
  };

  // Archive/Restore handlers with confirmation
  const handleArchiveClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowArchiveDialog(true);
  };

  const handleRestoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowRestoreDialog(true);
  };

  const handleConfirmArchive = async () => {
    setIsArchiving(true);
    try {
      await onArchive?.();
      setShowArchiveDialog(false);
    } catch (error) {
      console.error('Feil ved arkivering:', error);
    } finally {
      setIsArchiving(false);
    }
  };

  const handleConfirmRestore = async () => {
    setIsRestoring(true);
    try {
      await onRestore?.();
      setShowRestoreDialog(false);
    } catch (error) {
      console.error('Feil ved gjenåpning:', error);
    } finally {
      setIsRestoring(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('nb-NO');
    } catch {
      return dateString;
    }
  };

  return (
    <div
      className={`
        ${isArchived ? 'card-modern shadow-medium opacity-75 border-dashed border-2 border-jobblogg-warning/40' : 'card-elevated hover-lift hover-glow cursor-pointer focus-ring-enhanced interactive-press touch-feedback mobile-focus'}
        animate-slide-up group
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      style={{ animationDelay }}
      {...(!isArchived && {
        onClick: handleClick,
        onKeyDown: handleKeyDown,
        tabIndex: 0,
        role: "button",
        "aria-label": `Åpne prosjekt: ${title}`
      })}
    >
      {/* Project Image/Icon Area */}
      <figure className="px-6 pt-6 relative">
        {/* Archived Badge Overlay */}
        {isArchived && (
          <div className="absolute top-8 right-8 z-10">
            <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-jobblogg-warning text-white shadow-lg border-2 border-white">
              <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m0 6l-4-4-4 4" />
              </svg>
              ARKIVERT
            </div>
          </div>
        )}
        <div className={`w-full h-48 rounded-xl flex items-center justify-center transition-all duration-300 ${
          isArchived
            ? 'bg-jobblogg-neutral-dark/50 group-hover:bg-jobblogg-neutral-dark/60'
            : 'gradient-blue-soft group-hover:gradient-card-hover'
        }`}>
          <div className={`p-4 backdrop-blur-sm rounded-2xl shadow-soft group-hover:shadow-medium transition-all duration-300 group-hover:scale-105 ${
            isArchived ? 'bg-white/60' : 'bg-white/90'
          }`}>
            <svg
              className="w-12 h-12 text-jobblogg-primary group-hover:text-jobblogg-primary-light transition-all duration-300 group-hover:rotate-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        </div>
      </figure>

      {/* Project Content */}
      <div className="p-6">
        <h2 className="text-jobblogg-text-strong font-semibold text-xl group-hover:text-jobblogg-primary transition-colors duration-200">
          {title}
        </h2>
        
        <p className="text-jobblogg-text-medium text-sm leading-relaxed line-clamp-2 mb-4">
          {description || 'Ingen beskrivelse tilgjengelig'}
        </p>

        {/* Customer Information */}
        {customer && (
          <div className="bg-jobblogg-neutral-light rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 mb-1">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span className="text-jobblogg-text-strong font-medium text-sm">
                {customer.name}
              </span>
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                customer.type === 'firma'
                  ? 'bg-jobblogg-accent-soft text-jobblogg-accent'
                  : 'bg-jobblogg-primary-soft text-jobblogg-primary'
              }`}>
                {customer.type === 'firma' ? 'Firma' : 'Privat'}
              </span>
            </div>
            {customer.contactPerson && (
              <p className="text-jobblogg-text-muted text-xs mb-1">
                Kontakt: {customer.contactPerson}
              </p>
            )}
            {/* Address Display - Use structured fields with fallback to legacy address */}
            {(() => {
              // Check if we have structured address fields
              const hasStructuredAddress = isAddressComplete(
                customer.streetAddress,
                customer.postalCode,
                customer.city
              );

              // Format address based on available data
              let displayAddress = 'Ingen adresse';
              if (hasStructuredAddress && customer.streetAddress && customer.postalCode && customer.city) {
                displayAddress = formatAddress(
                  customer.streetAddress,
                  customer.postalCode,
                  customer.city,
                  customer.entrance
                );
              } else if (customer.address && customer.address.trim()) {
                // Fallback to legacy address field
                displayAddress = customer.address;
              }

              return displayAddress !== 'Ingen adresse' ? (
                <p className="text-jobblogg-text-muted text-xs flex items-center gap-1">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {displayAddress}
                </p>
              ) : null;
            })()}
          </div>
        )}

        {/* Project Metadata */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            {isArchived ? (
              <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-jobblogg-warning text-white shadow-sm">
                <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m0 6l-4-4-4 4" />
                </svg>
                ARKIVERT
                {archivedAt && (
                  <span className="ml-2 text-xs opacity-90">
                    {new Date(archivedAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' })}
                  </span>
                )}
              </div>
            ) : (
              <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary-soft text-jobblogg-primary">
                Aktiv
              </div>
            )}
            <span className="text-jobblogg-text-muted text-xs">
              {isArchived && archivedAt
                ? `Arkivert ${new Date(archivedAt).toLocaleDateString('nb-NO')}`
                : `Opprettet ${formatDate(updatedAt)}`
              }
            </span>
          </div>

          <div className="flex items-center gap-3">
            {/* Email Status Indicator - Only show for project owners */}
            {user?.id === projectOwnerId && (
              <EmailStatusIndicator
                projectId={_projectId}
                className="flex-shrink-0"
              />
            )}

            {/* Archive Actions */}
            {showArchiveActions && (
              <div onClick={(e) => e.stopPropagation()}>
                {isArchived ? (
                  <button
                    onClick={handleRestoreClick}
                    className="min-h-[44px] px-4 py-2 text-sm font-medium rounded-lg bg-jobblogg-accent-soft text-jobblogg-accent hover:bg-jobblogg-accent hover:text-white focus:outline-none focus:ring-2 focus:ring-jobblogg-accent/30 transition-all duration-200 flex items-center gap-2"
                    title="Gjenåpne prosjekt"
                    aria-label="Gjenåpne prosjekt"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Gjenåpne
                  </button>
                ) : (
                  <button
                    onClick={handleArchiveClick}
                    className="min-h-[44px] px-4 py-2 text-sm font-medium rounded-lg bg-jobblogg-warning-soft text-jobblogg-warning hover:bg-jobblogg-warning hover:text-white focus:outline-none focus:ring-2 focus:ring-jobblogg-warning/30 transition-all duration-200 flex items-center gap-2"
                    title="Arkiver prosjekt"
                    aria-label="Arkiver prosjekt"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V8z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6" />
                    </svg>
                    Arkiver
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Action Button - Only show for non-archived projects */}
        {!isArchived && (
          <div className="flex justify-end mt-6">
            <button
              className="btn-modern text-sm flex items-center gap-2 hover-bounce focus-ring-enhanced touch-target mobile-focus group/button bg-jobblogg-primary text-white"
              onClick={(e) => {
                e.stopPropagation();
                onClick();
              }}
              aria-label={`Åpne prosjektlogg for ${title}`}
            >
              <svg className="w-4 h-4 transition-transform duration-200 group-hover/button:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <span className="transition-transform duration-200 group-hover/button:translate-x-0.5">
                Åpne logg
              </span>
            </button>
          </div>
        )}
      </div>

      {/* Confirmation Dialogs */}
      <ConfirmDialog
        isOpen={showArchiveDialog}
        title="Arkiver prosjekt"
        message="Er du sikker på at du vil arkivere dette prosjektet? Prosjektet vil bli flyttet til arkivet og ikke lenger vises i aktive prosjekter. Alle data bevares og prosjektet kan gjenåpnes senere."
        confirmText="Arkiver prosjekt"
        cancelText="Avbryt"
        isDestructive={false}
        isLoading={isArchiving}
        onConfirm={handleConfirmArchive}
        onCancel={() => setShowArchiveDialog(false)}
      />

      <ConfirmDialog
        isOpen={showRestoreDialog}
        title="Gjenåpne prosjekt"
        message="Er du sikker på at du vil gjenåpne dette prosjektet? Prosjektet vil bli flyttet tilbake til aktive prosjekter og vises i hovedoversikten."
        confirmText="Gjenåpne prosjekt"
        cancelText="Avbryt"
        isDestructive={false}
        isLoading={isRestoring}
        onConfirm={handleConfirmRestore}
        onCancel={() => setShowRestoreDialog(false)}
      />
    </div>
  );
};

export default ProjectCard;
