import React from 'react';
import { CTAButton } from './CTAButton';

interface SecondaryButtonProps {
  /** Button content - clear, action-oriented Norwegian text */
  children: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state with Norwegian loading text */
  loading?: boolean;
  /** Loading text override (default: "Laster...") */
  loadingText?: string;
  /** Button size with responsive scaling */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Secondary CTA button component following JobbLogg 2025 design principles
 * Uses JobbLogg success green (#10B981) for positive supporting actions
 *
 * @example
 * ```tsx
 * <SecondaryButton onClick={handleLearnMore}>
 *   Les mer
 * </SecondaryButton>
 * ```
 */
export const SecondaryButton: React.FC<SecondaryButtonProps> = (props) => {
  return <CTAButton {...props} variant="secondary" />;
};
