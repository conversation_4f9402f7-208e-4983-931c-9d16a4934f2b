import React from 'react';

interface CTAButtonProps {
  /** Button content - clear, action-oriented Norwegian text */
  children: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state with Norwegian loading text */
  loading?: boolean;
  /** Loading text override (default: "Laster...") */
  loadingText?: string;
  /** Button variant following 2025 design principles */
  variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'danger';
  /** Button size with responsive scaling */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Modern CTA button component following JobbLogg 2025 design principles
 * 
 * Design Features:
 * - WCAG AA compliant with 44px minimum touch targets
 * - Responsive typography scaling (text-sm → text-base)
 * - Smooth micro-interactions with hardware acceleration
 * - Norwegian localization with professional terminology
 * - Modern flat design with subtle depth through shadows
 * 
 * Accessibility Features:
 * - High contrast ratios (WCAG AAA where possible)
 * - Keyboard navigation support (Enter/Space)
 * - Screen reader optimized with proper ARIA attributes
 * - Focus indicators with 2px offset rings
 * - Loading state announcements
 * 
 * @example
 * ```tsx
 * // Primary CTA - Main action
 * <CTAButton variant="primary" onClick={handleCreateProject}>
 *   Opprett prosjekt
 * </CTAButton>
 * 
 * // Secondary CTA - Supporting action
 * <CTAButton variant="secondary" size="lg">
 *   Les mer
 * </CTAButton>
 * 
 * // Loading state with custom text
 * <CTAButton 
 *   variant="primary"
 *   loading={isSubmitting}
 *   loadingText="Oppretter prosjekt..."
 * >
 *   Lagre endringer
 * </CTAButton>
 * ```
 */
export const CTAButton: React.FC<CTAButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  loading = false,
  loadingText = 'Laster...',
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  ariaLabel,
}) => {
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(event);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled && !loading) {
      event.preventDefault();
      if (onClick) {
        onClick(event as any); // Type assertion for keyboard event compatibility
      }
    }
  };

  // Variant styles following 2025 design principles
  const getVariantStyles = () => {
    const baseTransition = 'transition-colors duration-200 ease-in-out';
    
    switch (variant) {
      case 'secondary':
        return `
          bg-jobblogg-accent text-white border border-jobblogg-accent
          hover:bg-jobblogg-accent-light active:bg-jobblogg-accent-dark
          focus:ring-jobblogg-accent ${baseTransition}
        `;
      case 'tertiary':
        return `
          bg-transparent text-jobblogg-primary border-2 border-jobblogg-primary
          hover:bg-jobblogg-primary hover:text-white active:bg-jobblogg-primary-dark
          focus:ring-jobblogg-primary ${baseTransition}
        `;
      case 'ghost':
        return `
          bg-transparent text-jobblogg-text-medium border-none shadow-none
          hover:text-jobblogg-text-strong hover:bg-jobblogg-neutral hover:shadow-soft
          focus:ring-jobblogg-primary ${baseTransition}
        `;
      case 'danger':
        return `
          bg-jobblogg-error text-white border border-jobblogg-error
          hover:bg-jobblogg-error-dark active:bg-jobblogg-error-dark
          focus:ring-jobblogg-error ${baseTransition}
        `;
      default: // primary
        return `
          bg-jobblogg-primary text-white border border-jobblogg-primary
          hover:bg-jobblogg-primary-light active:bg-jobblogg-primary-dark
          focus:ring-jobblogg-primary ${baseTransition}
        `;
    }
  };

  // Size styles with WCAG AA compliant touch targets
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return `
          px-4 py-2.5 text-sm min-h-[44px] font-medium
        `;
      case 'lg':
        return `
          px-6 py-3.5 sm:px-8 sm:py-4 text-base sm:text-lg 
          min-h-[48px] font-semibold
        `;
      default: // md
        return `
          px-4 py-2.5 sm:px-5 sm:py-3 lg:px-6 lg:py-3.5
          text-sm sm:text-base min-h-[44px] font-semibold
        `;
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || loading}
      className={`
        inline-flex items-center justify-center gap-2
        rounded-xl transition-all duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        active:scale-[0.98] active:transition-transform active:duration-150
        shadow-soft hover:shadow-medium transition-shadow
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${fullWidth ? 'w-full' : ''}
        ${loading ? 'cursor-wait pointer-events-none' : 'cursor-pointer'}
        ${disabled ? 'pointer-events-none' : ''}
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      aria-disabled={disabled || loading}
      aria-label={ariaLabel || (loading ? loadingText : undefined)}
    >
      {loading ? (
        <div className="flex items-center gap-2">
          <div 
            className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"
            aria-hidden="true"
          />
          <span>{loadingText}</span>
        </div>
      ) : (
        <span>{children}</span>
      )}
    </button>
  );
};

export default CTAButton;
