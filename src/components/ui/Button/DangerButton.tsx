import React from 'react';
import { CTAButton } from './CTAButton';

interface DangerButtonProps {
  /** Button content - clear, action-oriented Norwegian text */
  children: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state with Norwegian loading text */
  loading?: boolean;
  /** Loading text override (default: "Laster...") */
  loadingText?: string;
  /** Button size with responsive scaling */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Danger CTA button component following JobbLogg 2025 design principles
 * Uses JobbLogg error red for destructive actions that require user attention
 *
 * Perfect for:
 * - Delete actions: "Slett prosjekt"
 * - Remove actions: "Fjern bruker"
 * - Archive actions: "Arkiver"
 * - Destructive confirmations: "Bekreft sletting"
 *
 * Features:
 * - High contrast red background for clear warning
 * - Darker red hover state for confirmation
 * - Proper accessibility with ARIA attributes
 * - Loading states for async destructive operations
 *
 * @example
 * ```tsx
 * <DangerButton
 *   onClick={handleDeleteProject}
 *   loading={isDeleting}
 *   loadingText="Sletter prosjekt..."
 * >
 *   Slett prosjekt
 * </DangerButton>
 *
 * <DangerButton onClick={handleRemoveUser} size="sm">
 *   Fjern bruker
 * </DangerButton>
 * ```
 */
export const DangerButton: React.FC<DangerButtonProps> = (props) => {
  return <CTAButton {...props} variant="danger" />;
};
