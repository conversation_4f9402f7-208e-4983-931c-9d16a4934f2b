import React, { useState, useEffect, useRef } from 'react';
import { TextMuted } from '../Typography';

interface SearchBarProps {
  /** Current search value */
  value: string;
  /** Search change handler with debounced input */
  onChange: (value: string) => void;
  /** Placeholder text */
  placeholder?: string;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  isLoading?: boolean;
  /** Search results count */
  resultsCount?: number;
  /** Clear search handler */
  onClear?: () => void;
}

/**
 * Mobile-first search bar component with debounced input
 * Follows JobbLogg design system with touch-friendly controls
 * 
 * @example
 * ```tsx
 * <SearchBar
 *   value={searchQuery}
 *   onChange={setSearchQuery}
 *   placeholder="Søk i prosjekter..."
 *   resultsCount={filteredProjects.length}
 * />
 * ```
 */
export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Søk i prosjekter...",
  debounceMs = 300,
  className = '',
  isLoading = false,
  resultsCount,
  onClear,
}) => {
  const [localValue, setLocalValue] = useState(value);
  const debounceRef = useRef<NodeJS.Timeout>();
  const inputRef = useRef<HTMLInputElement>(null);

  // Sync external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Debounced search
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (localValue !== value) {
        onChange(localValue);
      }
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [localValue, onChange, debounceMs, value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalValue(e.target.value);
  };

  const handleClear = () => {
    setLocalValue('');
    onChange('');
    onClear?.();
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClear();
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <svg 
            className={`w-5 h-5 transition-colors duration-200 ${
              isLoading ? 'text-jobblogg-primary animate-pulse' : 'text-jobblogg-text-medium'
            }`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
            />
          </svg>
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={localValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`
            w-full pl-12 pr-12 py-3 sm:py-2.5
            bg-white border border-jobblogg-border rounded-xl
            text-jobblogg-text-strong placeholder-jobblogg-text-muted
            focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:border-jobblogg-primary
            transition-all duration-200
            text-base sm:text-sm
            touch-target mobile-focus
          `}
          aria-label="Søk i prosjekter"
          aria-describedby={resultsCount !== undefined ? "search-results-count" : undefined}
        />

        {/* Clear Button */}
        {localValue && (
          <button
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-4 flex items-center touch-target mobile-focus"
            aria-label="Tøm søk"
            type="button"
          >
            <svg 
              className="w-5 h-5 text-jobblogg-text-medium hover:text-jobblogg-text-strong transition-colors duration-200" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </button>
        )}
      </div>

      {/* Results Count */}
      {resultsCount !== undefined && localValue && (
        <div className="mt-2 px-1">
          <TextMuted 
            id="search-results-count"
            className="text-xs"
          >
            {resultsCount === 0 
              ? 'Ingen resultater funnet' 
              : `${resultsCount} ${resultsCount === 1 ? 'resultat' : 'resultater'} funnet`
            }
          </TextMuted>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
