import React, { useState, useMemo } from 'react';
import { SearchBar } from './SearchBar';
import { FilterPanel, type FilterOptions } from './FilterPanel';
import { SortOptions, type SortOption } from './SortOptions';
import { useSearchFilters } from '../../../hooks/useSearchFilters';
import { useProjectSearch } from '../../../hooks/useProjectSearch';
import { TextMuted } from '../Typography';

// Project type based on schema analysis
interface Project {
  _id: string;
  name: string;
  description: string;
  createdAt: number;
  updatedAt?: number;
  isArchived?: boolean;
  customer?: {
    name: string;
    type: 'privat' | 'bedrift';
    contactPerson?: string;
    streetAddress?: string;
    postalCode?: string;
    city?: string;
    address?: string;
  } | null;
  userAccessLevel?: string;
  invitationStatus?: 'pending' | 'accepted' | 'declined' | 'expired';
  assignedAt?: number;
  subcontractorSpecialization?: string;
  mainContractorCompany?: string;
}

interface ProjectSearchContainerProps {
  /** Array of projects to search and filter */
  projects: Project[];
  /** Render function for filtered projects */
  children: (filteredProjects: Project[]) => React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show search results summary */
  showResultsSummary?: boolean;
  /** Custom placeholder for search input */
  searchPlaceholder?: string;
  /** Whether to persist filters in localStorage */
  persistFilters?: boolean;
  /** Storage key for persisted filters */
  storageKey?: string;
}

/**
 * Complete search, filter, and sort container for projects
 * Provides mobile-first UI with state persistence and performance optimization
 * 
 * @example
 * ```tsx
 * <ProjectSearchContainer
 *   projects={allProjects}
 *   persistFilters={true}
 *   storageKey="dashboard-project-filters"
 * >
 *   {(filteredProjects) => (
 *     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
 *       {filteredProjects.map((project) => (
 *         <ProjectCard key={project._id} {...project} />
 *       ))}
 *     </div>
 *   )}
 * </ProjectSearchContainer>
 * ```
 */
export const ProjectSearchContainer: React.FC<ProjectSearchContainerProps> = ({
  projects,
  children,
  className = '',
  showResultsSummary = true,
  searchPlaceholder = "Søk i prosjekter, kunder og adresser...",
  persistFilters = true,
  storageKey = 'jobblogg-project-search',
}) => {
  const [showFilters, setShowFilters] = useState(false);

  // Search and filter state management
  const {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortOption,
    setSortOption,
    clearAllFilters,
    activeFilterCount,
  } = useSearchFilters({
    persistState: persistFilters,
    storageKey,
  });

  // Apply search, filter, and sort logic
  const { filteredProjects, searchResults } = useProjectSearch({
    projects,
    searchQuery,
    filters,
    sortOption,
  });

  // Extract unique cities for location filter
  const availableCities = useMemo(() => {
    const cities = new Set<string>();
    projects.forEach(project => {
      if (project.customer?.city) {
        cities.add(project.customer.city);
      }
    });
    return Array.from(cities).sort();
  }, [projects]);

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Controls Row */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Bar */}
        <div className="flex-1">
          <SearchBar
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder={searchPlaceholder}
            resultsCount={searchResults.hasSearchQuery ? filteredProjects.length : undefined}
            onClear={handleClearSearch}
          />
        </div>

        {/* Sort Options */}
        <div className="flex-shrink-0">
          <SortOptions
            currentSort={sortOption}
            onChange={setSortOption}
            variant="dropdown"
          />
        </div>
      </div>

      {/* Filter Panel */}
      <FilterPanel
        filters={filters}
        onChange={setFilters}
        isExpanded={showFilters}
        onToggleExpanded={() => setShowFilters(!showFilters)}
        availableCities={availableCities}
        activeFilterCount={activeFilterCount}
      />

      {/* Results Summary */}
      {showResultsSummary && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 px-1">
          <div className="flex items-center gap-4">
            <TextMuted className="text-sm">
              Viser {filteredProjects.length} av {searchResults.totalProjects} prosjekter
            </TextMuted>
            
            {(searchResults.hasActiveFilters || searchResults.hasSearchQuery) && (
              <button
                onClick={clearAllFilters}
                className="text-sm text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 underline"
              >
                Nullstill alle filtre
              </button>
            )}
          </div>

          {/* Active Filters Summary */}
          {searchResults.hasActiveFilters && (
            <div className="flex flex-wrap gap-2">
              {filters.projectStatus !== 'all' && (
                <FilterBadge
                  label={`Status: ${getStatusLabel(filters.projectStatus)}`}
                  onRemove={() => setFilters({ ...filters, projectStatus: 'all' })}
                />
              )}
              {filters.customerType !== 'all' && (
                <FilterBadge
                  label={`Kunde: ${getCustomerTypeLabel(filters.customerType)}`}
                  onRemove={() => setFilters({ ...filters, customerType: 'all' })}
                />
              )}
              {filters.accessLevel !== 'all' && (
                <FilterBadge
                  label={`Rolle: ${getAccessLevelLabel(filters.accessLevel)}`}
                  onRemove={() => setFilters({ ...filters, accessLevel: 'all' })}
                />
              )}
              {filters.dateRange.type !== 'all' && (
                <FilterBadge
                  label={`Dato: ${getDateRangeLabel(filters.dateRange)}`}
                  onRemove={() => setFilters({ 
                    ...filters, 
                    dateRange: { type: 'all', period: 'all' }
                  })}
                />
              )}
              {filters.location.city.trim() && (
                <FilterBadge
                  label={`By: ${filters.location.city}`}
                  onRemove={() => setFilters({ 
                    ...filters, 
                    location: { city: '' }
                  })}
                />
              )}
            </div>
          )}
        </div>
      )}

      {/* Filtered Results */}
      {children(filteredProjects)}
    </div>
  );
};

// Helper component for filter badges
interface FilterBadgeProps {
  label: string;
  onRemove: () => void;
}

const FilterBadge: React.FC<FilterBadgeProps> = ({ label, onRemove }) => (
  <span className="inline-flex items-center gap-1 px-2 py-1 bg-jobblogg-primary/10 text-jobblogg-primary text-xs rounded-lg">
    {label}
    <button
      onClick={onRemove}
      className="hover:bg-jobblogg-primary/20 rounded-full p-0.5 transition-colors duration-200"
      aria-label={`Fjern filter: ${label}`}
    >
      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </span>
);

// Helper functions for filter labels
const getStatusLabel = (status: string): string => {
  switch (status) {
    case 'active': return 'Aktive';
    case 'archived': return 'Arkiverte';
    default: return status;
  }
};

const getCustomerTypeLabel = (type: string): string => {
  switch (type) {
    case 'privat': return 'Privat';
    case 'bedrift': return 'Bedrift';
    default: return type;
  }
};

const getAccessLevelLabel = (level: string): string => {
  switch (level) {
    case 'owner': return 'Prosjektleder';
    case 'collaborator': return 'Utførende';
    case 'subcontractor': return 'Underleverandør';
    case 'viewer': return 'Leser';
    default: return level;
  }
};

const getDateRangeLabel = (dateRange: FilterOptions['dateRange']): string => {
  const typeLabel = dateRange.type === 'created' ? 'Opprettet' : 'Oppdatert';
  const periodLabel = {
    today: 'i dag',
    week: 'denne uken',
    month: 'denne måneden',
    quarter: 'dette kvartalet',
    year: 'dette året',
  }[dateRange.period] || dateRange.period;
  
  return `${typeLabel} ${periodLabel}`;
};

export default ProjectSearchContainer;
