import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  Heading2,
  BodyText,
  TextMuted,
  SecondaryButton,
  PrimaryButton,
} from '../ui';
import { getSpecializationById } from '../../utils/specializations';

interface CollaborationHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  subcontractorCompanyId: string;
  subcontractorCompanyName: string;
  currentProjectId?: string;
}

export const CollaborationHistoryModal: React.FC<CollaborationHistoryModalProps> = ({
  isOpen,
  onClose,
  subcontractorCompanyId,
  subcontractorCompanyName,
  currentProjectId,
}) => {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState<'current' | 'other' | 'stats'>('current');

  // Get detailed collaboration history
  const collaborationHistory = useQuery(
    api.collaborationHistory.getCollaborationHistory,
    user?.id ? {
      mainContractorUserId: user.id,
      subcontractorCompanyId: subcontractorCompanyId as any,
      currentProjectId: currentProjectId as any,
    } : 'skip'
  );

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatDuration = (milliseconds: number) => {
    const days = Math.floor(milliseconds / (1000 * 60 * 60 * 24));
    if (days < 30) return `${days} dager`;
    if (days < 365) return `${Math.floor(days / 30)} måneder`;
    return `${Math.floor(days / 365)} år`;
  };

  if (!collaborationHistory) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="large">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-jobblogg-neutral rounded w-3/4"></div>
            <div className="h-4 bg-jobblogg-neutral rounded w-1/2"></div>
            <div className="space-y-2">
              <div className="h-4 bg-jobblogg-neutral rounded"></div>
              <div className="h-4 bg-jobblogg-neutral rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </Modal>
    );
  }

  if (!collaborationHistory.hasCollaborated) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="medium">
        <div className="p-6 text-center">
          <div className="w-16 h-16 bg-jobblogg-neutral/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </div>
          <Heading2 className="mb-2">Ingen tidligere samarbeid</Heading2>
          <BodyText className="text-jobblogg-text-medium mb-6">
            Du har ikke samarbeidet med {subcontractorCompanyName} tidligere.
          </BodyText>
          <SecondaryButton onClick={onClose}>
            Lukk
          </SecondaryButton>
        </div>
      </Modal>
    );
  }

  const tabs = [
    { id: 'current' as const, label: 'Nåværende prosjekt', count: collaborationHistory.currentProjectHistory?.previousAssignments.length || 0 },
    { id: 'other' as const, label: 'Andre prosjekter', count: collaborationHistory.otherProjectsHistory.projectCount },
    { id: 'stats' as const, label: 'Statistikk', count: null },
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="large">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <Heading2>Samarbeidshistorikk</Heading2>
            <button
              onClick={onClose}
              className="text-jobblogg-text-medium hover:text-jobblogg-text-strong transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <BodyText className="text-jobblogg-text-medium">
            {subcontractorCompanyName}
          </BodyText>
          
          {/* Overall Stats */}
          <div className="flex items-center gap-6 mt-4 p-4 bg-jobblogg-neutral/20 rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold text-jobblogg-primary">
                {collaborationHistory.totalProjects}
              </div>
              <TextMuted className="text-xs">Totalt prosjekter</TextMuted>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-jobblogg-success">
                {collaborationHistory.otherProjectsHistory.successfulCompletions}
              </div>
              <TextMuted className="text-xs">Fullførte</TextMuted>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-jobblogg-text-strong">
                {formatDate(collaborationHistory.firstCollaboration)}
              </div>
              <TextMuted className="text-xs">Første samarbeid</TextMuted>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-jobblogg-border mb-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-jobblogg-primary text-jobblogg-primary'
                  : 'border-transparent text-jobblogg-text-medium hover:text-jobblogg-text-strong'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className="ml-2 px-2 py-0.5 text-xs bg-jobblogg-neutral/50 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="min-h-[300px]">
          {activeTab === 'current' && (
            <div>
              {collaborationHistory.currentProjectHistory ? (
                <div className="space-y-4">
                  <BodyText className="text-jobblogg-text-medium">
                    Samarbeidshistorikk på nåværende prosjekt:
                  </BodyText>
                  {collaborationHistory.currentProjectHistory.previousAssignments.map((assignment) => (
                    <div key={assignment.assignmentId} className="p-4 border border-jobblogg-border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-jobblogg-text-strong">
                            {getSpecializationById(assignment.specialization)?.name || assignment.specialization}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            assignment.status === 'accepted' 
                              ? 'bg-jobblogg-success/10 text-jobblogg-success'
                              : assignment.status === 'pending'
                              ? 'bg-jobblogg-warning/10 text-jobblogg-warning'
                              : 'bg-jobblogg-neutral/20 text-jobblogg-text-medium'
                          }`}>
                            {assignment.status === 'accepted' ? 'Godtatt' : 
                             assignment.status === 'pending' ? 'Venter' : 'Aktiv'}
                          </span>
                        </div>
                        <TextMuted className="text-xs">
                          {formatDate(assignment.assignedAt)}
                        </TextMuted>
                      </div>
                      {assignment.notes && (
                        <BodyText className="text-sm text-jobblogg-text-medium">
                          {assignment.notes}
                        </BodyText>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <svg className="w-12 h-12 text-jobblogg-text-medium mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <BodyText className="text-jobblogg-text-medium">
                    Ingen tidligere samarbeid på dette prosjektet
                  </BodyText>
                </div>
              )}
            </div>
          )}

          {activeTab === 'other' && (
            <div className="space-y-4">
              {collaborationHistory.otherProjectsHistory.recentProjects.length > 0 ? (
                <>
                  <BodyText className="text-jobblogg-text-medium">
                    Siste {collaborationHistory.otherProjectsHistory.recentProjects.length} prosjekter:
                  </BodyText>
                  {collaborationHistory.otherProjectsHistory.recentProjects.map((project) => (
                    <div key={project.projectId} className="p-4 border border-jobblogg-border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <BodyText className="font-medium text-jobblogg-text-strong">
                          {project.projectName}
                        </BodyText>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          project.status === 'completed' 
                            ? 'bg-jobblogg-success/10 text-jobblogg-success'
                            : project.status === 'archived'
                            ? 'bg-jobblogg-neutral/20 text-jobblogg-text-medium'
                            : 'bg-jobblogg-primary/10 text-jobblogg-primary'
                        }`}>
                          {project.status === 'completed' ? 'Fullført' : 
                           project.status === 'archived' ? 'Arkivert' : 'Aktiv'}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-jobblogg-text-medium">
                        <span>{getSpecializationById(project.specialization)?.name || project.specialization}</span>
                        <span>•</span>
                        <span>{formatDate(project.startDate)}</span>
                        {project.endDate && (
                          <>
                            <span>•</span>
                            <span>Varighet: {formatDuration(project.endDate - project.startDate)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                  {collaborationHistory.otherProjectsHistory.projectCount > 5 && (
                    <TextMuted className="text-center text-sm">
                      ... og {collaborationHistory.otherProjectsHistory.projectCount - 5} flere prosjekter
                    </TextMuted>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <svg className="w-12 h-12 text-jobblogg-text-medium mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <BodyText className="text-jobblogg-text-medium">
                    Ingen andre prosjekter funnet
                  </BodyText>
                </div>
              )}
            </div>
          )}

          {activeTab === 'stats' && collaborationHistory.qualityMetrics && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-jobblogg-success/5 border border-jobblogg-success/20 rounded-lg text-center">
                  <div className="text-2xl font-bold text-jobblogg-success mb-1">
                    {Math.round(collaborationHistory.qualityMetrics.completionRate)}%
                  </div>
                  <TextMuted className="text-sm">Fullføringsgrad</TextMuted>
                </div>
                <div className="p-4 bg-jobblogg-primary/5 border border-jobblogg-primary/20 rounded-lg text-center">
                  <div className="text-2xl font-bold text-jobblogg-primary mb-1">
                    {formatDuration(collaborationHistory.qualityMetrics.averageProjectDuration)}
                  </div>
                  <TextMuted className="text-sm">Gjennomsnittlig varighet</TextMuted>
                </div>
                <div className="p-4 bg-jobblogg-accent/5 border border-jobblogg-accent/20 rounded-lg text-center">
                  <div className="text-2xl font-bold text-jobblogg-accent mb-1">
                    {collaborationHistory.qualityMetrics.responseTime > 0 
                      ? formatDuration(collaborationHistory.qualityMetrics.responseTime)
                      : 'N/A'
                    }
                  </div>
                  <TextMuted className="text-sm">Gjennomsnittlig responstid</TextMuted>
                </div>
              </div>

              {collaborationHistory.otherProjectsHistory.specializations.length > 0 && (
                <div>
                  <BodyText className="font-medium text-jobblogg-text-strong mb-3">
                    Spesialiseringer brukt:
                  </BodyText>
                  <div className="flex flex-wrap gap-2">
                    {collaborationHistory.otherProjectsHistory.specializations.map((spec) => (
                      <span
                        key={spec}
                        className="px-3 py-1 bg-jobblogg-accent/10 text-jobblogg-accent text-sm rounded-full border border-jobblogg-accent/20"
                      >
                        {getSpecializationById(spec)?.name || spec}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-jobblogg-border">
          <SecondaryButton onClick={onClose}>
            Lukk
          </SecondaryButton>
        </div>
      </div>
    </Modal>
  );
};

export default CollaborationHistoryModal;
