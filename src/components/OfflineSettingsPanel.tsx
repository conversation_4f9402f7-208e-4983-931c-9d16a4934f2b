import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { usePWA } from '../hooks/usePWA';
import { useOfflineProjects } from '../hooks/useOfflineProjects';
import { offlineStorage } from '../utils/offlineStorage';
import { offlineImageStorage } from '../utils/offlineImageStorage';
import { gdprCompliance } from '../utils/offlineEncryption';
import { PrimaryButton, SecondaryButton } from './ui';

interface OfflineSettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

/**
 * Comprehensive offline settings and data management panel
 * Provides GDPR-compliant controls for offline data
 */
export const OfflineSettingsPanel: React.FC<OfflineSettingsPanelProps> = ({
  isOpen,
  onClose,
  className = ''
}) => {
  const { user } = useUser();
  const { canAccessOfflineData, isOnline } = usePWA();
  const { hasOfflineProjects, offlineProjectCount, pendingUploads } = useOfflineProjects();
  
  const [storageUsage, setStorageUsage] = useState({ used: 0, available: 0, percentage: 0 });
  const [isClearing, setIsClearing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showConsentRevoke, setShowConsentRevoke] = useState(false);
  const [lastSync, setLastSync] = useState<string | null>(null);

  // Load storage usage and sync info
  useEffect(() => {
    if (!isOpen) return;

    const loadStorageInfo = async () => {
      try {
        // Get storage usage
        const usage = offlineStorage.getStorageUsage();
        setStorageUsage(usage);

        // Get last sync time
        const syncTime = localStorage.getItem('jobblogg-last-sync');
        setLastSync(syncTime);
      } catch (error) {
        console.error('[OfflineSettings] Failed to load storage info:', error);
      }
    };

    loadStorageInfo();
  }, [isOpen]);

  if (!isOpen) return null;

  const handleClearOfflineData = async () => {
    if (!user?.id) return;

    setIsClearing(true);
    try {
      // Clear offline storage
      await offlineStorage.clearOfflineData();
      
      // Clear offline images
      await offlineImageStorage.clearAllImages();
      
      // Update storage usage
      const usage = offlineStorage.getStorageUsage();
      setStorageUsage(usage);
      
      setShowDeleteConfirm(false);
      console.log('[OfflineSettings] Offline data cleared successfully');
    } catch (error) {
      console.error('[OfflineSettings] Failed to clear offline data:', error);
    } finally {
      setIsClearing(false);
    }
  };

  const handleRevokeConsent = async () => {
    setIsClearing(true);
    try {
      // Revoke GDPR consent and clear all data
      gdprCompliance.revokeConsent();
      
      // Clear offline storage
      await offlineStorage.logoutCleanup();
      
      // Clear offline images
      await offlineImageStorage.clearAllImages();
      
      setShowConsentRevoke(false);
      onClose();
      
      // Refresh page to reflect changes
      window.location.reload();
    } catch (error) {
      console.error('[OfflineSettings] Failed to revoke consent:', error);
    } finally {
      setIsClearing(false);
    }
  };

  const formatStorageSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${Math.round(bytes / 1024)} KB`;
    return `${Math.round(bytes / 1024 / 1024 * 100) / 100} MB`;
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'Aldri';
    try {
      return new Date(dateString).toLocaleString('nb-NO');
    } catch {
      return 'Ukjent';
    }
  };

  const dataProcessingInfo = gdprCompliance.getDataProcessingInfo();

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 ${className}`}>
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-jobblogg-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-jobblogg-primary rounded-xl flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-jobblogg-text-strong">
                  Offline-innstillinger
                </h2>
                <p className="text-sm text-jobblogg-text-medium">
                  Administrer offline data og personvern
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full hover:bg-jobblogg-neutral transition-colors flex items-center justify-center"
            >
              <svg className="w-5 h-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Overview */}
          <div className="bg-jobblogg-neutral-soft p-4 rounded-lg">
            <h3 className="font-medium text-jobblogg-text-strong mb-3">Status oversikt</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-jobblogg-text-medium">Offline-modus:</span>
                <div className={`font-medium ${canAccessOfflineData ? 'text-jobblogg-success' : 'text-jobblogg-text-muted'}`}>
                  {canAccessOfflineData ? '✅ Aktivert' : '❌ Ikke aktivert'}
                </div>
              </div>
              <div>
                <span className="text-jobblogg-text-medium">Tilkobling:</span>
                <div className={`font-medium ${isOnline ? 'text-jobblogg-success' : 'text-jobblogg-warning'}`}>
                  {isOnline ? '🌐 Online' : '📱 Offline'}
                </div>
              </div>
              <div>
                <span className="text-jobblogg-text-medium">Offline prosjekter:</span>
                <div className="font-medium text-jobblogg-text-strong">
                  {hasOfflineProjects ? `📁 ${offlineProjectCount}` : '📁 0'}
                </div>
              </div>
              <div>
                <span className="text-jobblogg-text-medium">Venter på opplasting:</span>
                <div className={`font-medium ${pendingUploads > 0 ? 'text-jobblogg-warning' : 'text-jobblogg-text-strong'}`}>
                  {pendingUploads > 0 ? `⏳ ${pendingUploads}` : '✅ 0'}
                </div>
              </div>
            </div>
          </div>

          {/* Storage Usage */}
          <div className="space-y-3">
            <h3 className="font-medium text-jobblogg-text-strong">Lagringsbruk</h3>
            <div className="bg-jobblogg-neutral-soft p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-jobblogg-text-medium">Brukt plass:</span>
                <span className="text-sm font-medium text-jobblogg-text-strong">
                  {formatStorageSize(storageUsage.used)} av {formatStorageSize(storageUsage.available)}
                </span>
              </div>
              <div className="w-full bg-jobblogg-neutral rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    storageUsage.percentage > 90 ? 'bg-jobblogg-error' :
                    storageUsage.percentage > 70 ? 'bg-jobblogg-warning' :
                    'bg-jobblogg-primary'
                  }`}
                  style={{ width: `${Math.min(storageUsage.percentage, 100)}%` }}
                />
              </div>
              <div className="text-xs text-jobblogg-text-muted mt-1">
                {Math.round(storageUsage.percentage)}% brukt
              </div>
            </div>
          </div>

          {/* Sync Information */}
          <div className="space-y-3">
            <h3 className="font-medium text-jobblogg-text-strong">Synkronisering</h3>
            <div className="bg-jobblogg-neutral-soft p-4 rounded-lg text-sm">
              <div className="flex justify-between">
                <span className="text-jobblogg-text-medium">Sist synkronisert:</span>
                <span className="font-medium text-jobblogg-text-strong">
                  {formatDate(lastSync)}
                </span>
              </div>
            </div>
          </div>

          {/* Data Management */}
          <div className="space-y-3">
            <h3 className="font-medium text-jobblogg-text-strong">Databehandling</h3>
            <div className="bg-jobblogg-neutral-soft p-4 rounded-lg text-sm space-y-2">
              <div>
                <span className="font-medium text-jobblogg-text-strong">Formål:</span>
                <p className="text-jobblogg-text-medium">{dataProcessingInfo.purpose}</p>
              </div>
              <div>
                <span className="font-medium text-jobblogg-text-strong">Lagringsperiode:</span>
                <p className="text-jobblogg-text-medium">{dataProcessingInfo.retention}</p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <h3 className="font-medium text-jobblogg-text-strong">Handlinger</h3>
            
            {/* Clear Offline Data */}
            <div className="flex items-center justify-between p-4 bg-jobblogg-warning-soft rounded-lg">
              <div>
                <h4 className="font-medium text-jobblogg-text-strong">Slett offline data</h4>
                <p className="text-sm text-jobblogg-text-medium">
                  Fjern alle lokalt lagrede prosjekter og bilder
                </p>
              </div>
              <SecondaryButton
                onClick={() => setShowDeleteConfirm(true)}
                disabled={!hasOfflineProjects && pendingUploads === 0}
                className="text-jobblogg-warning hover:bg-jobblogg-warning hover:text-white"
              >
                Slett data
              </SecondaryButton>
            </div>

            {/* Revoke Consent */}
            <div className="flex items-center justify-between p-4 bg-jobblogg-error-soft rounded-lg">
              <div>
                <h4 className="font-medium text-jobblogg-text-strong">Trekk tilbake samtykke</h4>
                <p className="text-sm text-jobblogg-text-medium">
                  Deaktiver offline-modus og slett all data permanent
                </p>
              </div>
              <SecondaryButton
                onClick={() => setShowConsentRevoke(true)}
                className="text-jobblogg-error hover:bg-jobblogg-error hover:text-white"
              >
                Trekk tilbake
              </SecondaryButton>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60">
            <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-jobblogg-warning-soft rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-jobblogg-text-strong">Slett offline data</h3>
                  <p className="text-sm text-jobblogg-text-medium">Denne handlingen kan ikke angres</p>
                </div>
              </div>
              
              <p className="text-sm text-jobblogg-text-medium mb-6">
                Dette vil slette alle offline prosjekter og bilder som ikke er synkronisert. 
                Data som allerede er synkronisert vil ikke påvirkes.
              </p>
              
              <div className="flex gap-3">
                <SecondaryButton
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1"
                >
                  Avbryt
                </SecondaryButton>
                <PrimaryButton
                  onClick={handleClearOfflineData}
                  loading={isClearing}
                  className="flex-1 bg-jobblogg-warning hover:bg-jobblogg-warning-hover"
                >
                  {isClearing ? 'Sletter...' : 'Slett data'}
                </PrimaryButton>
              </div>
            </div>
          </div>
        )}

        {/* Consent Revoke Confirmation Modal */}
        {showConsentRevoke && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60">
            <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-jobblogg-error-soft rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-jobblogg-text-strong">Trekk tilbake samtykke</h3>
                  <p className="text-sm text-jobblogg-text-medium">Permanent handling</p>
                </div>
              </div>
              
              <p className="text-sm text-jobblogg-text-medium mb-6">
                Dette vil permanent deaktivere offline-modus og slette all lokalt lagret data. 
                Du må gi nytt samtykke for å bruke offline-funksjonalitet igjen.
              </p>
              
              <div className="flex gap-3">
                <SecondaryButton
                  onClick={() => setShowConsentRevoke(false)}
                  className="flex-1"
                >
                  Avbryt
                </SecondaryButton>
                <PrimaryButton
                  onClick={handleRevokeConsent}
                  loading={isClearing}
                  className="flex-1 bg-jobblogg-error hover:bg-jobblogg-error-hover"
                >
                  {isClearing ? 'Trekker tilbake...' : 'Trekk tilbake samtykke'}
                </PrimaryButton>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OfflineSettingsPanel;
