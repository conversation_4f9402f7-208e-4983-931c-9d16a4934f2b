import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import type { Id } from '../../../convex/_generated/dataModel';
import { ChatContainerProps, MessageFormData, ChatError, OptimisticMessage, MessageWithDisplayInfo } from '../../types/chat';
import { MessageInput } from './MessageInput';
import { MessageText, MessageTextWithPreviews } from './MessageText';
import { FileAttachment } from './FileAttachment';
import { EnhancedEmojiReactions } from './EnhancedEmojiReactions';
import { TextMuted, TextStrong } from '../ui';

// Embedded chat props with additional configuration
export interface EmbeddedChatContainerProps extends ChatContainerProps {
  maxHeight?: string;
  // Customer information for shared projects (enables proper user name display in reactions)
  customerInfo?: {
    name: string;
    type?: 'privat' | 'bedrift' | 'firma';
    contactPerson?: string;
    phone?: string;
    email?: string;
  } | null;
}

export const EmbeddedChatContainer: React.FC<EmbeddedChatContainerProps> = ({
  logId,
  userId,
  userRole,
  className = '',
  maxHeight = '400px',
  customerInfo
}) => {
  const [error, setError] = useState<ChatError | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Convex hooks - always fetch for new always-visible design
  const messagesData = useQuery(
    api.messages.getMessagesWithDisplayNames,
    {
      logId,
      userId,
      userRole,
      limit: 50, // Increased limit for always-visible view
      cursor: undefined
    }
  );

  const typingIndicatorsData = useQuery(
    api.messages.getTypingIndicators,
    {
      logId,
      userId
    }
  );

  // Smooth typing indicators with debouncing and minimum display duration
  const [smoothTypingIndicators, setSmoothTypingIndicators] = React.useState<any[]>([]);
  const [isShowingTyping, setIsShowingTyping] = React.useState(false);
  const smoothTypingTimeoutRef = React.useRef<NodeJS.Timeout>();
  const hideTimeoutRef = React.useRef<NodeJS.Timeout>();
  const lastShowTimeRef = React.useRef<number>(0);

  React.useEffect(() => {
    const hasTypingIndicators = typingIndicatorsData && typingIndicatorsData.length > 0;

    if (hasTypingIndicators) {
      // Clear any pending hide timeout
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
        hideTimeoutRef.current = undefined;
      }

      // Update the indicators data immediately
      setSmoothTypingIndicators(typingIndicatorsData);

      // Show typing indicators if not already showing
      if (!isShowingTyping) {
        setIsShowingTyping(true);
        lastShowTimeRef.current = Date.now();
      }

      // Reset the typing timeout (debounce hiding)
      if (smoothTypingTimeoutRef.current) {
        clearTimeout(smoothTypingTimeoutRef.current);
      }

      smoothTypingTimeoutRef.current = setTimeout(() => {
        // Calculate minimum display time (1 second minimum)
        const elapsed = Date.now() - lastShowTimeRef.current;
        const minDisplayTime = 1000;
        const remainingTime = Math.max(0, minDisplayTime - elapsed);

        hideTimeoutRef.current = setTimeout(() => {
          setIsShowingTyping(false);
        }, remainingTime);
      }, 500); // Hide after 500ms of no updates

    } else if (isShowingTyping) {
      // Clear any pending typing timeout
      if (smoothTypingTimeoutRef.current) {
        clearTimeout(smoothTypingTimeoutRef.current);
        smoothTypingTimeoutRef.current = undefined;
      }

      // Calculate minimum display time
      const elapsed = Date.now() - lastShowTimeRef.current;
      const minDisplayTime = 1000;
      const remainingTime = Math.max(0, minDisplayTime - elapsed);

      hideTimeoutRef.current = setTimeout(() => {
        setIsShowingTyping(false);
      }, remainingTime);
    }

    // Cleanup function
    return () => {
      if (smoothTypingTimeoutRef.current) {
        clearTimeout(smoothTypingTimeoutRef.current);
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, [typingIndicatorsData, isShowingTyping]);

  const sendMessage = useMutation(api.messages.sendMessage);
  const startTypingMutation = useMutation(api.messages.startTyping);
  const stopTypingMutation = useMutation(api.messages.stopTyping);
  const markAsRead = useMutation(api.messages.markAsRead);
  const addReaction = useMutation(api.messages.addReaction);
  const removeReaction = useMutation(api.messages.removeReaction);
  const replaceReaction = useMutation(api.messages.replaceReaction);

  // Handle sending messages with optimistic updates
  const handleSendMessage = useCallback(async (data: MessageFormData) => {
    try {
      setError(null);

      // Create optimistic message for immediate UI feedback
      const optimisticId = `optimistic-${Date.now()}-${Math.random()}`;
      const optimisticMessage: OptimisticMessage = {
        _id: optimisticId,
        _creationTime: Date.now(),
        logId,
        parentId: data.parentId,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file ? {
          url: data.file.url || '',
          name: data.file.name,
          size: data.file.size,
          type: data.file.type
        } : undefined,
        createdAt: Date.now(),
        isOptimistic: true,
        isSending: true
      };

      setOptimisticMessages(prev => [...prev, optimisticMessage]);

      // Send the actual message
      await sendMessage({
        logId,
        parentId: data.parentId,
        senderId: userId,
        senderRole: userRole,
        text: data.text,
        file: data.file
      });

      // Remove optimistic message after successful send
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));

    } catch (err) {
      // Mark optimistic message as failed
      setOptimisticMessages(prev =>
        prev.map(msg =>
          msg._id === optimisticId
            ? { ...msg, isSending: false, sendError: err instanceof Error ? err.message : 'Kunne ikke sende melding' }
            : msg
        )
      );
      setError({
        type: 'network',
        message: err instanceof Error ? err.message : 'Kunne ikke sende melding',
        timestamp: Date.now()
      });
    }
  }, [logId, userId, userRole, sendMessage]);

  // Typing indicator handlers
  const handleTypingStart = useCallback(async () => {
    console.log('🔵 EmbeddedChat - handleTypingStart called', { isTyping, logId, userId, userRole });
    if (!isTyping) {
      setIsTyping(true);
      try {
        console.log('🔵 EmbeddedChat - Calling startTypingMutation');
        await startTypingMutation({
          logId,
          userId,
          userRole
        });
        console.log('🔵 EmbeddedChat - startTypingMutation success');
      } catch (error) {
        console.error('Failed to start typing indicator:', error);
      }
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      handleTypingStop();
    }, 3000);
  }, [isTyping, startTypingMutation, logId, userId, userRole]);

  const handleTypingStop = useCallback(async () => {
    console.log('🔴 EmbeddedChat - handleTypingStop called', { logId, userId });
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    setIsTyping(false);

    try {
      console.log('🔴 EmbeddedChat - Calling stopTypingMutation');
      await stopTypingMutation({
        logId,
        userId
      });
      console.log('🔴 EmbeddedChat - stopTypingMutation success');
    } catch (error) {
      console.error('Failed to stop typing indicator:', error);
    }
  }, [stopTypingMutation, logId, userId]);

  // Use user name mapping from backend if available, otherwise create fallback mapping
  const userNames = useMemo(() => {
    // If backend provides userNames mapping, use it
    if (messagesData?.userNames) {
      return messagesData.userNames;
    }

    // Fallback: create basic mapping for current user
    const mapping: Record<string, string> = {};

    if (userRole === 'customer') {
      mapping[userId] = 'Kunde';
    } else if (userRole === 'contractor') {
      // For contractors, we'll rely on backend userNames mapping
      // Only add fallback if absolutely necessary
      mapping[userId] = 'Leverandør';
    }

    return mapping;
  }, [messagesData?.userNames, userId, userRole]);

  // Merge optimistic messages with real messages
  const mergedMessages = useMemo(() => {
    if (!messagesData?.messages) return [];

    const realMessages = messagesData.messages;

    const optimisticAsDisplay: MessageWithDisplayInfo[] = optimisticMessages.map(opt => ({
      ...opt,
      senderDisplayName: userRole === 'contractor' ? (userNames[userId] || 'Leverandør') : 'Meg', // Use actual name or fallback
      isOwnMessage: true,
      replies: []
    }));

    return [...realMessages, ...optimisticAsDisplay];
  }, [messagesData?.messages, optimisticMessages, userRole, userId, userNames]);

  // Handle message reactions with one-reaction-per-user constraint
  const handleReaction = useCallback(async (messageId: Id<"messages">, emoji: string) => {
    try {
      setError(null);

      console.log('🎯 EmbeddedChatContainer: handleReaction called', { messageId, emoji, userId });

      // Find the message to check current user reactions
      const message = mergedMessages.find(m => m._id === messageId);

      if (!message) {
        console.warn('⚠️ EmbeddedChatContainer: Message not found', { messageId });
        return;
      }

      // Check user's current reactions on this message
      const userReactions = (message.reactions || []).filter(r => r.userIds.includes(userId));
      const currentUserReaction = userReactions[0]; // Should only be one after this fix
      const isSameEmoji = currentUserReaction?.emoji === emoji;

      console.log('🔍 EmbeddedChatContainer: User reaction state', {
        userReactions,
        currentUserReaction,
        isSameEmoji,
        targetEmoji: emoji
      });

      // Use replaceReaction mutation which enforces one-reaction-per-user
      console.log('🔄 EmbeddedChatContainer: Using replaceReaction mutation');
      const result = await replaceReaction({ messageId, userId, emoji });

      console.log('✅ EmbeddedChatContainer: Reaction replacement completed', {
        action: result.action,
        emoji: result.emoji
      });
    } catch (err) {
      console.error('❌ EmbeddedChatContainer: Reaction failed:', err);
      setError({
        type: 'unknown',
        message: err instanceof Error ? err.message : 'Kunne ikke reagere på melding',
        timestamp: Date.now()
      });
    }
  }, [mergedMessages, userId, replaceReaction]);

  // Mark messages as read when messages are loaded (always visible now)
  useEffect(() => {
    if (messagesData?.messages.length) {
      markAsRead({ logId, userId, userRole }).catch(console.error);
    }
  }, [markAsRead, logId, userId, userRole, messagesData?.messages.length]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [mergedMessages]);

  // Get message count for display
  const messageCount = mergedMessages.length;
  const hasMessages = messageCount > 0;

  // Priority emoji reactions for the summary bar (as specified in requirements)
  const priorityEmojis = ['👍', '❤️', '😊', '😢', '😮', '😡'];

  // Aggregate reactions from all messages in the conversation
  const aggregatedReactions = useMemo(() => {
    const reactionMap = new Map<string, { emoji: string; count: number; userNames: string[] }>();

    mergedMessages.forEach(message => {
      if (message.reactions) {
        message.reactions.forEach(reaction => {
          const existing = reactionMap.get(reaction.emoji);
          if (existing) {
            existing.count += reaction.count;
            // Add unique user names (simplified - in real implementation, you'd get actual names)
            const newUserNames = reaction.userIds.map((_, index) => `Bruker ${index + 1}`);
            existing.userNames = [...new Set([...existing.userNames, ...newUserNames])];
          } else {
            reactionMap.set(reaction.emoji, {
              emoji: reaction.emoji,
              count: reaction.count,
              userNames: reaction.userIds.map((_, index) => `Bruker ${index + 1}`)
            });
          }
        });
      }
    });

    // Sort by priority emojis first, then by count
    return Array.from(reactionMap.values()).sort((a, b) => {
      const aPriority = priorityEmojis.indexOf(a.emoji);
      const bPriority = priorityEmojis.indexOf(b.emoji);

      if (aPriority !== -1 && bPriority !== -1) {
        return aPriority - bPriority; // Both are priority, sort by priority order
      } else if (aPriority !== -1) {
        return -1; // a is priority, b is not
      } else if (bPriority !== -1) {
        return 1; // b is priority, a is not
      } else {
        return b.count - a.count; // Neither is priority, sort by count
      }
    });
  }, [mergedMessages]);

  // Format reaction display text
  const formatReactionText = (reaction: { emoji: string; count: number; userNames: string[] }) => {
    if (reaction.count === 1) {
      return `${reaction.emoji} 1 (${reaction.userNames[0]})`;
    } else if (reaction.count <= 3) {
      return `${reaction.emoji} ${reaction.count} (${reaction.userNames.join(', ')})`;
    } else {
      const displayNames = reaction.userNames.slice(0, 2).join(', ');
      const remaining = reaction.count - 2;
      return `${reaction.emoji} ${reaction.count} (${displayNames}, +${remaining} ${remaining === 1 ? 'annen' : 'andre'})`;
    }
  };

  return (
    <div className={`jobblogg-comment-section bg-jobblogg-card-bg rounded-lg ${className}`}>
      {/* Clean Summary Bar */}
      {hasMessages && (
        <div className="flex items-center justify-between px-4 py-3 border-b border-jobblogg-border bg-jobblogg-card-bg">
          <div className="text-sm text-jobblogg-text-muted font-medium">
            {messageCount} {messageCount === 1 ? 'kommentar' : 'kommentarer'}
          </div>
          {aggregatedReactions.length > 0 && (
            <div className="flex items-center gap-3">
              {aggregatedReactions.slice(0, 3).map((reaction, index) => (
                <span key={index} className="text-sm text-jobblogg-text-muted">
                  {formatReactionText(reaction)}
                </span>
              ))}
              {aggregatedReactions.length > 3 && (
                <span className="text-sm text-jobblogg-text-muted">
                  +{aggregatedReactions.length - 3} flere
                </span>
              )}
            </div>
          )}
        </div>
      )}



      {/* Enhanced Message Thread Display - Always visible */}
      <div className="bg-white">
        {/* Messages Area with consistent spacing */}
        <div
          className="overflow-y-auto px-4 py-4 space-y-4"
          style={{ maxHeight }}
        >
          {mergedMessages.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-jobblogg-neutral-secondary flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="text-jobblogg-text-muted text-sm font-medium mb-1">
                Ingen kommentarer ennå
              </div>
              <TextMuted className="text-xs">
                {userRole === 'contractor'
                  ? 'Vær den første til å kommentere!'
                  : 'Start en samtale med kontraktøren!'}
              </TextMuted>
            </div>
          ) : (
            mergedMessages.map((message) => (
              <div key={message._id} className={`flex ${message.isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                {/* Message Content */}
                <div className={`max-w-[70%]`}>
                  {/* Simplified Header - Single line with essential info */}
                  <div className={`flex items-center gap-2 mb-2 ${message.isOwnMessage ? 'flex-row-reverse' : ''}`}>
                    <TextStrong className="text-sm font-semibold text-jobblogg-text-strong">
                      {message.isOwnMessage
                        ? 'Meg'
                        : message.senderDisplayName}
                    </TextStrong>
                    <TextMuted className="text-xs">
                      {new Date(message.createdAt).toLocaleString('nb-NO', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </TextMuted>
                  </div>

                  {/* Clean Chat Bubble */}
                  <div className={`relative rounded-2xl px-4 py-3 shadow-sm ${
                    message.isOwnMessage
                      ? 'bg-jobblogg-primary text-white'
                      : 'bg-jobblogg-neutral-secondary text-jobblogg-text-strong'
                  } ${message.isOwnMessage ? 'rounded-br-md' : 'rounded-bl-md'}`}>

                    {/* Message Text with improved typography */}
                    {message.text && (
                      <MessageTextWithPreviews
                        text={message.text}
                        showPreviews={true}
                        previewStyle="compact"
                        className={`text-sm leading-relaxed ${
                          message.isOwnMessage
                            ? 'text-white'
                            : 'text-jobblogg-text-strong'
                        }`}
                      />
                    )}

                    {/* File Attachment */}
                    {message.file && (
                      <div className={`${message.text ? 'mt-3' : ''}`}>
                        <FileAttachment file={message.file} />
                      </div>
                    )}

                    {/* Clean Status indicators */}
                    {message.isOptimistic && (
                      <div className={`flex items-center gap-2 mt-2 text-xs font-medium ${
                        message.isOwnMessage ? 'text-white/70' : 'text-jobblogg-text-muted'
                      }`}>
                        {message.isSending && <span>• Sender...</span>}
                        {message.sendError && <span className="text-jobblogg-error">• Feil ved sending</span>}
                      </div>
                    )}
                  </div>

                  {/* Read Status Indicator - Only for own messages that have been read */}
                  {message.isOwnMessage && message.readBy && (() => {
                    // Find the other user's read timestamp (not the sender's own timestamp)
                    const otherUserReadTimestamp = Object.entries(message.readBy).find(
                      ([readUserId]) => readUserId !== userId
                    )?.[1];

                    if (otherUserReadTimestamp) {
                      return (
                        <div className={`mt-1 ${message.isOwnMessage ? 'flex justify-end' : ''}`}>
                          <TextMuted className="text-xs text-jobblogg-text-muted font-normal">
                            Lest {new Date(otherUserReadTimestamp).toLocaleString('nb-NO', {
                              day: '2-digit',
                              month: '2-digit',
                              year: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </TextMuted>
                        </div>
                      );
                    }
                    return null;
                  })()}

                  {/* Message Reactions - Subtle metadata below message */}
                  <div className={`${message.isOwnMessage ? 'flex justify-end' : ''}`}>
                    <EnhancedEmojiReactions
                      reactions={message.reactions || []}
                      messageId={message._id}
                      userId={userId}
                      onReaction={handleReaction}
                      className=""
                      userNames={userNames}
                    />
                  </div>
                </div>
              </div>
            ))
          )}

          {/* Smooth Typing Indicators - Mobile-responsive */}
          <div
            className={`transition-all duration-300 ease-in-out overflow-hidden ${
              isShowingTyping ? 'max-h-16 opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="flex gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-3 border-t border-jobblogg-border bg-jobblogg-neutral-secondary/30">
              <div className="flex-shrink-0">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-jobblogg-text-muted/20 flex items-center justify-center">
                  <div className="flex space-x-0.5 sm:space-x-1">
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-jobblogg-text-muted rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-jobblogg-text-muted rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-jobblogg-text-muted rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
              <div className="flex-1">
                <div className="text-xs sm:text-sm text-jobblogg-text-muted italic transition-opacity duration-200">
                  {smoothTypingIndicators.length === 1
                    ? `${smoothTypingIndicators[0].displayName} skriver...`
                    : `${smoothTypingIndicators.length} personer skriver...`
                  }
                </div>
              </div>
            </div>
          </div>

          <div ref={messagesEndRef} />
        </div>

        {/* Clean Error Display */}
        {error && (
          <div className="px-4 py-3 bg-jobblogg-error-soft border-l-4 border-jobblogg-error">
            <TextMuted className="text-sm text-jobblogg-error font-medium">
              {error.message}
            </TextMuted>
          </div>
        )}

        {/* Message Input - Always visible */}
        <div className="border-t border-jobblogg-border bg-white">
          <MessageInput
            logId={logId}
            userId={userId}
            userRole={userRole}
            onSend={handleSendMessage}
            placeholder="Skriv en melding..."
            className="border-0 rounded-none"
            onTypingStart={handleTypingStart}
            onTypingStop={handleTypingStop}
          />
        </div>
      </div>
    </div>
  );
};
