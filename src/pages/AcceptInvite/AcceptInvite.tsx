import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { SignUp, useUser } from '@clerk/clerk-react';
import { nbNO } from '@clerk/localizations';
import { api } from '../../../convex/_generated/api';
import {
  PageLayout,
  Heading1,
  Heading2,
  BodyText,
  StatsCard,
  PrimaryButton
} from '../../components/ui';

const AcceptInvite: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, isSignedIn } = useUser();

  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [invitationProcessed, setInvitationProcessed] = useState<boolean>(false);

  // Invitation data
  const [invitationData, setInvitationData] = useState<any>(null);

  // Cleanup stored token on component unmount
  React.useEffect(() => {
    return () => {
      // Only cleanup if user navigates away without completing signup
      const currentPath = window.location.pathname;
      if (!currentPath.includes('/accept-invite')) {
        localStorage.removeItem('jobblogg-invitation-token');
      }
    };
  }, []);

  const acceptMagicLinkInvitation = useMutation(api.teamManagement.acceptMagicLinkInvitation);

  // Debug: Log when AcceptInvite component mounts
  useEffect(() => {
    console.log('🎯 AcceptInvite component mounted:', {
      currentPath: window.location.pathname,
      searchParams: Object.fromEntries(searchParams.entries()),
      isSignedIn,
      userId: user?.id
    });

    // Navigation prevention removed - backup processing in Onboarding Guard handles redirects
  }, [invitationProcessed, invitationToken]);
  
  // Query invitation data without requiring authentication
  // Skip query only if invitation is already processed or no token
  const invitationInfo = useQuery(
    api.teamManagement.getMagicLinkInvitationData,
    (invitationToken && !invitationProcessed) ? { invitationToken } : "skip"
  );

  // Get invitation token from URL parameters or localStorage
  useEffect(() => {
    const token = searchParams.get('token');
    const storedToken = localStorage.getItem('jobblogg-invitation-token');

    if (token) {
      // Fresh invitation link - store token for later use
      setInvitationToken(token);
      localStorage.setItem('jobblogg-invitation-token', token);
      console.log('🔗 Invitation token found in URL and stored:', token);
    } else if (storedToken) {
      // Returning from email verification - use stored token
      setInvitationToken(storedToken);
      console.log('🔄 Using stored invitation token after redirect:', storedToken);
    } else {
      setError('Invitasjonstoken mangler i URL');
    }
  }, [searchParams]);

  // Update invitation data when loaded
  useEffect(() => {
    console.log('🔍 Invitation data update:', {
      invitationInfo,
      hasInvitationInfo: !!invitationInfo,
      invitationInfoIsNull: invitationInfo === null,
      isSignedIn,
      invitationToken
    });

    if (invitationInfo) {
      console.log('✅ Setting invitation data:', invitationInfo);
      setInvitationData(invitationInfo);
    } else if (invitationInfo === null) {
      // Invitation already processed, clean up and redirect
      console.log('✅ Invitation already processed, cleaning up...');
      setInvitationToken(null);
      localStorage.removeItem('jobblogg-invitation-token');
      if (isSignedIn) {
        navigate('/', { replace: true });
      }
    }
  }, [invitationInfo, isSignedIn, navigate, invitationToken]);

  // Robust invitation processing - monitors for user sign-in completion
  useEffect(() => {
    const processInvitation = async () => {
      // Only process if:
      // 1. User is signed in
      // 2. We have invitation data
      // 3. We haven't processed the invitation yet
      // 4. We have a valid invitation token
      if (!isSignedIn || !user || !invitationData || invitationProcessed || !invitationToken) {
        console.log('🔍 Robust processing - Waiting for conditions:', {
          isSignedIn,
          hasUser: !!user,
          hasInvitationData: !!invitationData,
          invitationProcessed,
          hasInvitationToken: !!invitationToken,
          currentPath: window.location.pathname
        });
        return;
      }

      // Backup processing in Onboarding Guard handles redirects, so we don't need aggressive checks here

      console.log('🔄 Robust invitation processing triggered:', {
        isSignedIn,
        userId: user.id,
        invitationToken,
        invitationProcessed,
        currentPath: window.location.pathname
      });

      try {
        setInvitationProcessed(true); // Prevent multiple processing

        // Call the invitation acceptance function
        const invitationResult = await acceptMagicLinkInvitation({
          invitationToken,
          clerkUserId: user.id,
          finalEmail: invitationData.email,
          finalFirstName: invitationData.firstName,
          finalLastName: invitationData.lastName,
          finalPhone: invitationData.phone,
        });

        console.log('✅ Robust invitation processing successful:', invitationResult);

        // Clean up stored token
        localStorage.removeItem('jobblogg-invitation-token');

        // Set contractor onboarding as completed in localStorage
        const storageKey = `jobblogg-contractor-completed-${user.id}`;
        localStorage.setItem(storageKey, 'true');

        // Set team member flag
        const teamMemberKey = `jobblogg-team-member-${user.id}`;
        localStorage.setItem(teamMemberKey, 'true');

        console.log('✅ Team member flags set in localStorage');

        // Clean up token to prevent further queries
        setInvitationToken(null);

        // Redirect to main dashboard
        setTimeout(() => {
          console.log('🔄 Redirecting team member to main dashboard...');
          navigate('/', { replace: true });
        }, 1500); // Shorter delay since we're more confident about the process

      } catch (error: any) {
        console.error('❌ Robust invitation processing failed:', error);
        setError('Kunne ikke akseptere invitasjon. Prøv igjen eller kontakt support.');
        setInvitationProcessed(false); // Allow retry
      }
    };

    processInvitation();
  }, [isSignedIn, user, invitationData, invitationProcessed, invitationToken, acceptMagicLinkInvitation, navigate]);

  // Auto-fill form fields after Clerk component loads
  useEffect(() => {
    if (invitationData) {
      console.log('🔄 Forsøker å fylle ut feltene med:', {
        email: invitationData.email,
        firstName: invitationData.firstName,
        lastName: invitationData.lastName
      });

      const timer = setTimeout(() => {
        // Try to fill email field
        const emailInput = document.querySelector('input[name="emailAddress"]') as HTMLInputElement;
        if (emailInput && invitationData.email) {
          emailInput.value = invitationData.email;
          emailInput.dispatchEvent(new Event('input', { bubbles: true }));
          console.log('✅ E-post fylt ut:', invitationData.email);
        } else {
          console.log('❌ E-post felt ikke funnet eller ingen e-post data');
        }

        // Try to fill first name field
        const firstNameInput = document.querySelector('input[name="firstName"]') as HTMLInputElement;
        if (firstNameInput && invitationData.firstName) {
          firstNameInput.value = invitationData.firstName;
          firstNameInput.dispatchEvent(new Event('input', { bubbles: true }));
          console.log('✅ Fornavn fylt ut:', invitationData.firstName);
        } else {
          console.log('❌ Fornavn felt ikke funnet eller ingen fornavn data');
        }

        // Try to fill last name field
        const lastNameInput = document.querySelector('input[name="lastName"]') as HTMLInputElement;
        if (lastNameInput && invitationData.lastName) {
          lastNameInput.value = invitationData.lastName;
          lastNameInput.dispatchEvent(new Event('input', { bubbles: true }));
          console.log('✅ Etternavn fylt ut:', invitationData.lastName);
        } else {
          console.log('❌ Etternavn felt ikke funnet eller ingen etternavn data');
        }
      }, 1000); // Wait longer for Clerk to render

      return () => clearTimeout(timer);
    }
  }, [invitationData]);

  // Handle SignUp completion and invitation acceptance (BACKUP METHOD)
  const handleSignUpComplete = async (signUp: any) => {
    console.log('🔄 Clerk onSignUpComplete callback triggered (backup method)');

    if (!invitationToken || !invitationData) {
      console.log('⚠️ Backup method: Missing invitation data, relying on robust method');
      return;
    }

    if (invitationProcessed) {
      console.log('✅ Backup method: Invitation already processed by robust method');
      return;
    }

    try {
      console.log('🔄 Backup method: Clerk SignUp completed - processing invitation...');
      console.log('📊 Backup method SignUp data:', {
        createdUserId: signUp.createdUserId,
        emailAddress: signUp.emailAddress,
        firstName: signUp.firstName,
        lastName: signUp.lastName
      });

      setInvitationProcessed(true); // Mark as processed

      // Accept the invitation in Convex
      const invitationResult = await acceptMagicLinkInvitation({
        invitationToken,
        clerkUserId: signUp.createdUserId!,
        finalEmail: signUp.emailAddress || invitationData.email,
        finalFirstName: signUp.firstName || invitationData.firstName,
        finalLastName: signUp.lastName || invitationData.lastName,
        finalPhone: invitationData.phone,
      });

      console.log('✅ Backup method: Magic link invitation accepted successfully:', invitationResult);

      // Clean up stored token
      localStorage.removeItem('jobblogg-invitation-token');

      // Set contractor onboarding as completed in localStorage to prevent routing to onboarding
      const storageKey = `jobblogg-contractor-completed-${signUp.createdUserId}`;
      localStorage.setItem(storageKey, 'true');

      console.log('✅ Contractor onboarding marked as completed in localStorage');

      // Also set a team member flag to help the onboarding guard
      const teamMemberKey = `jobblogg-team-member-${signUp.createdUserId}`;
      localStorage.setItem(teamMemberKey, 'true');

      console.log('✅ Team member flag set in localStorage');

      // Add a longer delay to ensure Convex has synced the changes before redirect
      // Team members need more time for database sync than regular users
      setTimeout(() => {
        console.log('🔄 Redirecting team member to main dashboard...');
        navigate('/', { replace: true });
      }, 2000); // 2 second delay to allow Convex sync for team members

    } catch (error: any) {
      console.error('❌ Feil ved akseptering av invitasjon:', error);
      setError('Kunne ikke akseptere invitasjon. Prøv igjen eller kontakt support.');
    }
  };

  // Loading state while invitation data loads
  if (invitationToken && !invitationInfo && !error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-jobblogg-neutral/20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
          <BodyText className="text-jobblogg-text-muted">
            Laster invitasjonsinformasjon...
          </BodyText>
        </div>
      </div>
    );
  }

  // Error state (invalid or expired invitation)
  if (error && !invitationInfo) {
    return (
      <PageLayout 
        title="Invitasjon ikke funnet"
        subtitle="Det oppstod et problem med invitasjonslinken"
        containerWidth="narrow"
      >
        <div className="text-center space-y-8">
          {/* Error Icon */}
          <div className="w-20 h-20 mx-auto bg-jobblogg-error/10 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          <div className="space-y-4">
            <Heading1 className="text-jobblogg-error">Ugyldig invitasjon</Heading1>
            <BodyText className="text-lg">{error}</BodyText>
          </div>

          <PrimaryButton onClick={() => navigate('/')}>
            Gå til JobbLogg
          </PrimaryButton>
        </div>
      </PageLayout>
    );
  }

  // Main magic link registration form
  return (
    <PageLayout 
      title="Bli med i teamet!"
      subtitle={`Du har blitt invitert til å bli med i ${invitationData?.companyName || 'teamet'}`}
      containerWidth="narrow"
    >
      <div className="text-center space-y-8">
        {/* Magic Link Icon */}
        <div className="w-20 h-20 mx-auto bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
          <svg className="w-10 h-10 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
        </div>

        {/* Welcome Message */}
        <div className="space-y-4">
          <Heading1>✨ Magic Link Invitasjon</Heading1>
          <BodyText className="text-lg">
            Informasjonen nedenfor er pre-utfylt fra invitasjonen. Du kan redigere alt før du fullfører registreringen.
          </BodyText>
        </div>

        {/* Invitation Details */}
        {invitationData && (
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
            <Heading2>Invitasjonsdetaljer</Heading2>
            
            <div className="grid gap-4 sm:grid-cols-2">
              <StatsCard
                title="Bedrift"
                value={invitationData.companyName}
                variant="primary"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                }
              />
              
              <StatsCard
                title="Rolle"
                value={invitationData.role === 'administrator' ? 'Administrator' : 'Utførende'}
                variant="accent"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
              />
            </div>
          </div>
        )}

        {/* Clerk SignUp Component */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
          <div className="mb-6 text-center">
            <Heading2>Fullfør registrering</Heading2>
            <BodyText className="text-jobblogg-text-muted mt-2">
              Informasjonen nedenfor er pre-utfylt fra invitasjonen
            </BodyText>
          </div>

          {invitationData ? (
            <SignUp
              key={`signup-${invitationData.email}-${invitationData.firstName}-${invitationData.lastName}`}
              path="/accept-invite"
              routing="path"
              localization={nbNO}
              initialValues={{
                emailAddress: invitationData.email || '',
                firstName: invitationData.firstName || '',
                lastName: invitationData.lastName || '',
              }}
              signUpProps={{
                initialValues: {
                  emailAddress: invitationData.email || '',
                  firstName: invitationData.firstName || '',
                  lastName: invitationData.lastName || '',
                }
              }}
              appearance={{
                elements: {
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent",
                  headerTitle: "text-jobblogg-text-strong text-xl font-semibold",
                  headerSubtitle: "text-jobblogg-text-muted",
                  socialButtonsBlockButton: "border-jobblogg-border hover:bg-jobblogg-neutral/10",
                  formButtonPrimary: "bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white font-medium py-3 px-6 rounded-lg transition-colors",
                  formFieldInput: "border-jobblogg-border focus:border-jobblogg-primary focus:ring-jobblogg-primary/20",
                  formFieldLabel: "text-jobblogg-text-strong font-medium",
                  identityPreviewText: "text-jobblogg-text-muted",
                  formFieldErrorText: "text-jobblogg-error",
                },
                variables: {
                  colorPrimary: "#2563EB", // jobblogg-primary
                  colorText: "#1F2937", // jobblogg-text-strong
                  colorTextSecondary: "#4B5563", // jobblogg-text-muted
                  colorDanger: "#DC2626", // jobblogg-error
                  borderRadius: "0.5rem",
                }
              }}
              // Prevent ALL automatic redirects to allow invitation processing
              signUpFallbackRedirectUrl={window.location.href}
              signUpForceRedirectUrl={window.location.href}
              signInFallbackRedirectUrl={window.location.href}
              signInForceRedirectUrl={window.location.href}
              onSignUpComplete={handleSignUpComplete}
              beforeSignUp={async (signUp) => {
                console.log('🔄 beforeSignUp callback - pre-filling data...');
                if (invitationData) {
                  try {
                    await signUp.update({
                      emailAddress: invitationData.email || '',
                      firstName: invitationData.firstName || '',
                      lastName: invitationData.lastName || '',
                    });
                    console.log('✅ Data pre-filled via beforeSignUp');
                  } catch (error) {
                    console.log('❌ Error in beforeSignUp:', error);
                  }
                }
              }}
            />
          ) : (
            <div className="text-center py-8">
              <BodyText className="text-jobblogg-text-muted">
                Laster invitasjonsdata...
              </BodyText>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-4 bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg">
              <BodyText className="text-jobblogg-error text-sm">
                {error}
              </BodyText>
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
};

export default AcceptInvite;
