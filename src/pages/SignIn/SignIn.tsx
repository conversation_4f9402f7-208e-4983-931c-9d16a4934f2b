import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { Link, useSearchParams } from 'react-router-dom';
import { JobbLoggLogo } from '../../components/ui';

const SignIn: React.FC = () => {
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';

  return (
    <div className="min-h-screen bg-white flex flex-col lg:flex-row">
      {/* Left Side - Brand & Context Section */}
      <div className="lg:w-1/2 relative bg-gradient-to-br from-jobblogg-primary via-jobblogg-primary-dark to-jobblogg-primary-light overflow-hidden">
        {/* Background Pattern/Texture */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        {/* Content Container */}
        <div className="relative z-10 h-full flex flex-col justify-between p-8 lg:p-12 min-h-[400px] lg:min-h-screen">
          {/* Logo and Header */}
          <div className="space-y-8">
            {/* Logo */}
            <div className="animate-fade-in">
              <div className="inline-flex bg-white/95 backdrop-blur-sm rounded-xl px-4 py-3 shadow-soft border border-white/20">
                <JobbLoggLogo size="lg" />
              </div>
            </div>

            {/* Value Proposition */}
            <div className="space-y-4 animate-slide-up" style={{ animationDelay: '200ms' }}>
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-white leading-tight">
                Dokumenter og kommuniser<br />
                undervegs.
              </h1>
              <p className="text-lg lg:text-xl text-white/90 max-w-md leading-relaxed">
                Hold kunden oppdatert mens du jobber – ikke bare når du er ferdig.



              </p>
            </div>
          </div>

          {/* Customer Testimonial */}
          <div className="animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                    </svg>
                  </div>
                </div>
                <div>
                  <p className="text-white/95 text-sm lg:text-base leading-relaxed mb-3">
                    "JobbLogg har gjort kommunikasjonen med kundene mine så mye enklere. De kan følge fremgangen i sanntid!"
                  </p>
                  <div className="text-white/80 text-sm">
                    <div className="font-medium">Lars Eriksen</div>
                    <div className="text-white/70">Eriksen Bygg AS</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Sign-in Section */}
      <div className="lg:w-1/2 flex items-center justify-center p-8 lg:p-12">
        <div className="w-full max-w-md space-y-8">
          {/* Clerk SignIn Component */}
          <div className="animate-scale-in" style={{ animationDelay: '300ms' }}>
            <ClerkSignIn
              fallbackRedirectUrl={redirectUrl}
              signUpUrl="/sign-up"
              appearance={{
                variables: {
                  fontFamily: 'Inter, system-ui, sans-serif',
                  fontSize: '16px',
                  borderRadius: '0.75rem', // 12px - consistent with logo container
                  colorPrimary: '#2563EB', // jobblogg-primary
                  colorText: '#111827', // jobblogg-text-strong
                  colorTextSecondary: '#4B5563', // jobblogg-text-medium
                  colorBackground: '#ffffff',
                  colorInputBackground: '#ffffff',
                  colorInputText: '#111827',
                  spacingUnit: '1rem',
                },
                elements: {
                  rootBox: "w-full font-sans",
                  card: "shadow-none border-none bg-transparent p-0 w-full",

                  // Header styling with JobbLogg typography
                  headerTitle: "text-2xl font-bold text-jobblogg-text-strong mb-2 font-sans leading-tight",
                  headerSubtitle: "text-jobblogg-text-medium text-base mb-8 font-sans leading-relaxed",

                  // Enhanced primary button with JobbLogg styling
                  formButtonPrimary: [
                    "bg-jobblogg-primary hover:bg-jobblogg-primary-dark active:bg-jobblogg-primary-dark",
                    "text-white font-medium text-base font-sans",
                    "py-3 px-6 h-12 w-full",
                    "rounded-xl border-none cursor-pointer",
                    "shadow-soft hover:shadow-medium",
                    "transition-all duration-200 ease-out",
                    "hover:scale-[1.02] active:scale-[0.98]",
                    "focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:ring-offset-2",
                    "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                  ].join(" "),

                  // Enhanced input fields with JobbLogg styling
                  formFieldInput: [
                    "border border-jobblogg-border bg-white text-jobblogg-text-strong",
                    "focus:border-jobblogg-primary focus:ring-2 focus:ring-jobblogg-primary/20",
                    "hover:border-jobblogg-border-dark",
                    "rounded-xl px-4 py-3 h-12 w-full text-base font-sans",
                    "transition-all duration-200 ease-out",
                    "placeholder:text-jobblogg-text-muted",
                    "focus:outline-none focus:ring-offset-0"
                  ].join(" "),

                  // Form field labels with JobbLogg typography
                  formFieldLabel: "text-jobblogg-text-strong font-medium text-sm mb-2 font-sans",

                  // Enhanced footer links with JobbLogg styling
                  footerActionLink: [
                    "text-jobblogg-primary hover:text-jobblogg-primary-dark",
                    "font-medium text-sm font-sans",
                    "transition-colors duration-200 ease-out",
                    "hover:underline underline-offset-2",
                    "focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:ring-offset-2 rounded"
                  ].join(" "),

                  // Divider styling
                  dividerLine: "bg-jobblogg-border h-px",
                  dividerText: "text-jobblogg-text-muted text-sm font-sans font-medium",

                  // Enhanced social buttons with JobbLogg styling
                  socialButtonsBlockButton: [
                    "border border-jobblogg-border bg-white text-jobblogg-text-strong",
                    "hover:bg-jobblogg-neutral/30 hover:border-jobblogg-border-dark",
                    "active:bg-jobblogg-neutral/50",
                    "rounded-xl py-3 px-4 h-12 w-full",
                    "font-medium text-sm font-sans",
                    "transition-all duration-200 ease-out",
                    "focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:ring-offset-2",
                    "shadow-soft hover:shadow-medium"
                  ].join(" "),

                  // Error text styling
                  formFieldErrorText: "text-jobblogg-error text-sm mt-1 font-sans font-medium",

                  // Success text styling
                  formFieldSuccessText: "text-jobblogg-accent text-sm mt-1 font-sans font-medium",

                  // Loading spinner
                  spinner: "text-jobblogg-primary w-5 h-5",

                  // Form field action (forgot password, etc.)
                  formFieldAction: [
                    "text-jobblogg-primary hover:text-jobblogg-primary-dark",
                    "text-sm font-medium font-sans",
                    "transition-colors duration-200 ease-out",
                    "hover:underline underline-offset-2",
                    "focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:ring-offset-1 rounded"
                  ].join(" "),
                }
              }}
            />
          </div>

          {/* Footer Links */}
          <div className="text-center animate-slide-up" style={{ animationDelay: '500ms' }}>
            <div className="flex items-center justify-center space-x-6 text-sm text-jobblogg-text-muted">
              <Link
                to="/privacy-policy"
                className="hover:text-jobblogg-primary transition-colors duration-200"
              >
                Personvern
              </Link>
              <span className="text-jobblogg-border">•</span>
              <Link
                to="/terms-of-service"
                className="hover:text-jobblogg-primary transition-colors duration-200"
              >
                Bruksvilkår
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
