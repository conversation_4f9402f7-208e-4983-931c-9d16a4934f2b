import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, EmptyState } from '../../components/ui';
import { ConversationCard } from '../../components/chat/ConversationCard';

/**
 * Conversations overview page
 * Shows all chat conversations with unread counts and navigation
 */
const Conversations: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useUser();
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  // Protect against console hook issues during component initialization
  React.useEffect(() => {
    // Store original console methods
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn
    };

    // Temporarily override console methods to prevent object-to-primitive conversion
    const safeConsoleMethod = (method: 'log' | 'error' | 'warn') => (...args: any[]) => {
      try {
        const safeArgs = args.map(arg => {
          if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return arg;
        });
        originalConsole[method](...safeArgs);
      } catch (error) {
        originalConsole[method]('[Console Error]', String(error));
      }
    };

    console.log = safeConsoleMethod('log');
    console.error = safeConsoleMethod('error');
    console.warn = safeConsoleMethod('warn');

    // Cleanup on unmount
    return () => {
      console.log = originalConsole.log;
      console.error = originalConsole.error;
      console.warn = originalConsole.warn;
    };
  }, []);

  // Get unread message counts and conversation data
  const unreadData = useQuery(
    api.messages.getUnreadCounts,
    user ? {
      userId: user.id,
      userRole: "contractor" as const // TODO: Determine role based on user data
    } : "skip"
  );

  // Debug logging with safe serialization
  React.useEffect(() => {
    if (unreadData) {
      try {
        console.log('[Conversations] Unread data loaded:', {
          totalUnread: unreadData.totalUnread,
          conversationCount: unreadData.conversationCounts?.length || 0,
          hasConversations: Array.isArray(unreadData.conversationCounts)
        });
      } catch (error) {
        console.error('[Conversations] Error logging unread data:', error instanceof Error ? error.message : String(error));
      }
    }
  }, [unreadData]);

  // Get all projects to show conversations for
  const projects = useQuery(
    api.projects.getByUserWithCustomers,
    user ? { userId: user.id } : "skip"
  );

  if (!user) {
    return (
      <PageLayout title="Samtaler" showBackButton backUrl="/" showFooter={false}>
        <div className="flex items-center justify-center h-64">
          <p className="text-jobblogg-text-muted">Du må være logget inn for å se samtaler.</p>
        </div>
      </PageLayout>
    );
  }

  if (unreadData === undefined || projects === undefined) {
    return (
      <PageLayout title="Samtaler" showBackButton backUrl="/" showFooter={false}>
        <div className="flex items-center justify-center h-64">
          <div className="text-jobblogg-text-muted">Laster samtaler...</div>
        </div>
      </PageLayout>
    );
  }

  // Filter conversations based on selected filter
  const filteredConversations = filter === 'unread' 
    ? unreadData.conversationCounts 
    : unreadData.conversationCounts; // TODO: Show all conversations, not just unread

  const totalUnread = unreadData.totalUnread;
  const hasConversations = filteredConversations.length > 0;

  return (
    <PageLayout
      title="Samtaler"
      showBackButton
      backUrl="/"
      showFooter={false}
      headerActions={
        <div className="flex items-center gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
              filter === 'all'
                ? 'bg-jobblogg-primary text-white'
                : 'text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10'
            }`}
          >
            Alle
          </button>
          <button
            onClick={() => setFilter('unread')}
            className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
              filter === 'unread'
                ? 'bg-jobblogg-warning text-white'
                : 'text-jobblogg-text-medium hover:text-jobblogg-warning hover:bg-jobblogg-warning/10'
            }`}
          >
            Uleste ({totalUnread})
          </button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Summary */}
        {totalUnread > 0 && (
          <div className="bg-jobblogg-warning/10 border border-jobblogg-warning/20 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-jobblogg-warning/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <p className="font-medium text-jobblogg-text-strong">
                  Du har {totalUnread} uleste melding{totalUnread !== 1 ? 'er' : ''}
                </p>
                <p className="text-sm text-jobblogg-text-muted">
                  Fordelt på {filteredConversations.length} samtale{filteredConversations.length !== 1 ? 'r' : ''}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Conversations List */}
        {hasConversations ? (
          <div className="space-y-4">
            <Heading2>
              {filter === 'unread' ? 'Uleste samtaler' : 'Alle samtaler'}
            </Heading2>
            
            <div className="space-y-3">
              {filteredConversations.map((conversation, index) => {
                // Safe key generation
                const safeProjectId = conversation?.projectId ? String(conversation.projectId) : `unknown-${index}`;
                const safeLogId = conversation?.logId ? String(conversation.logId) : `unknown-${index}`;
                const key = `conversation-${index}-${safeProjectId}-${safeLogId}`;

                return (
                  <ConversationCard
                    key={key}
                    conversation={conversation}
                    onClick={() => {
                      try {
                        const projectId = conversation?.projectId ? String(conversation.projectId) : '';
                        if (projectId) {
                          navigate(`/project/${projectId}`);
                        } else {
                          console.error('[Conversations] Invalid project ID for navigation');
                        }
                      } catch (error) {
                        console.error('[Conversations] Navigation error:', error instanceof Error ? error.message : String(error));
                      }
                    }}
                  />
                );
              })}
            </div>
          </div>
        ) : (
          <EmptyState
            title={filter === 'unread' ? "🎉 Ingen uleste meldinger!" : "💬 Ingen samtaler ennå"}
            description={
              filter === 'unread'
                ? "Alle meldingene dine er lest. Flott jobbet med å holde deg oppdatert!"
                : "Samtaler vil vises her når du eller dine kunder sender meldinger i prosjektloggene."
            }
            actionLabel={filter === 'unread' ? "Vis alle samtaler" : "Gå til prosjekter"}
            onAction={() => filter === 'unread' ? setFilter('all') : navigate('/')}
          />
        )}
      </div>
    </PageLayout>
  );
};

// Default export required for lazy loading
export default Conversations;
