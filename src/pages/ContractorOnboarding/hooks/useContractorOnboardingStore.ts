import { useState } from 'react';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';

// Default form data
const defaultFormData: ContractorOnboardingFormData = {
  companyName: '',
  orgNumber: '',
  contactPerson: '',
  phone: '',
  email: '',
  streetAddress: '',
  postalCode: '',
  city: '',
  entrance: '',
  notes: '',
  useCustomAddress: false,
  lockedFields: {
    orgNumber: false,
    address: false
  },
  companySelected: false,
  // Specialization data
  primarySpecialization: undefined,
  secondarySpecializations: [],
  specializationSource: undefined
};

/**
 * Custom hook for managing contractor onboarding state
 * 
 * Provides centralized state management for the multi-step onboarding wizard
 * including form data, step navigation, and Brønnøysundregisteret integration state
 */
export const useContractorOnboardingStore = () => {
  // Form data state
  const [formData, setFormData] = useState<ContractorOnboardingFormData>(defaultFormData);
  
  // Step navigation state
  const [currentStep, setCurrentStep] = useState(1);
  
  // Brønnøysundregisteret integration state
  const [brregData, setBrregData] = useState<any>(null);
  const [brregFetchedAt, setBrregFetchedAt] = useState<number | null>(null);
  const [useCustomAddress, setUseCustomAddress] = useState(false);
  const [lockedFields, setLockedFields] = useState<{
    orgNumber?: boolean;
    address?: boolean;
  }>({
    orgNumber: false,
    address: false
  });
  const [companySelected, setCompanySelected] = useState(false);

  // Contact details field state (for Step 3)
  const [autoPopulatedFields, setAutoPopulatedFields] = useState<{
    contactPerson: boolean;
    phone: boolean;
    email: boolean;
  }>({ contactPerson: false, phone: false, email: false });

  const [fieldOverrides, setFieldOverrides] = useState<{
    phone: boolean;
    email: boolean;
  }>({ phone: false, email: false });

  // Helper function to reset all state
  const resetState = () => {
    setFormData(defaultFormData);
    setCurrentStep(1);
    setBrregData(null);
    setBrregFetchedAt(null);
    setUseCustomAddress(false);
    setLockedFields({
      orgNumber: false,
      address: false
    });
    setCompanySelected(false);
    setAutoPopulatedFields({ contactPerson: false, phone: false, email: false });
    setFieldOverrides({ phone: false, email: false });
  };

  // Helper function to validate current step
  const validateStep = (step: number): { isValid: boolean; errors: { [key: string]: string } } => {
    const errors: { [key: string]: string } = {};

    switch (step) {
      case 1:
        // Introduction step - no validation needed
        return { isValid: true, errors: {} };

      case 2:
        // Company lookup step
        if (!formData.companyName.trim()) {
          errors.companyName = 'Bedriftsnavn er påkrevd';
        }
        if (!formData.orgNumber.trim()) {
          errors.orgNumber = 'Organisasjonsnummer er påkrevd';
        } else if (!/^\d{9}$/.test(formData.orgNumber.replace(/\s/g, ''))) {
          errors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
        }
        break;

      case 3:
        // Contact details step
        if (!formData.contactPerson.trim()) {
          errors.contactPerson = 'Kontaktperson er påkrevd';
        }
        if (!formData.phone.trim()) {
          errors.phone = 'Telefonnummer er påkrevd';
        } else {
          // Check that we have exactly 8 digits for Norwegian mobile numbers
          const digits = formData.phone.replace(/\D/g, '');
          if (digits.length !== 8) {
            errors.phone = 'Telefonnummer må være 8 siffer';
          } else if (!/^[4-9]/.test(digits)) {
            // Norwegian mobile numbers typically start with 4, 5, 9
            errors.phone = 'Ugyldig norsk mobilnummer';
          }
        }
        if (!formData.email.trim()) {
          errors.email = 'E-postadresse er påkrevd';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
          errors.email = 'Ugyldig e-postadresse format';
        }
        break;

      case 4:
        // Confirmation step - validate all previous steps
        const step2Validation = validateStep(2);
        const step3Validation = validateStep(3);
        
        return {
          isValid: step2Validation.isValid && step3Validation.isValid,
          errors: { ...step2Validation.errors, ...step3Validation.errors }
        };
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };

  // Helper function to check if step can be navigated to
  const canNavigateToStep = (targetStep: number): boolean => {
    // Can always go back
    if (targetStep <= currentStep) {
      return true;
    }

    // Can only go forward if current step is valid
    const validation = validateStep(currentStep);
    return validation.isValid;
  };

  // Helper function to get completion percentage
  const getCompletionPercentage = (): number => {
    return Math.round((currentStep / 4) * 100);
  };

  // Helper function to check if onboarding is complete
  const isOnboardingComplete = (): boolean => {
    const validation = validateStep(4);
    return validation.isValid;
  };

  return {
    // Form data
    formData,
    setFormData,
    
    // Step navigation
    currentStep,
    setCurrentStep,
    
    // Brønnøysundregisteret integration
    brregData,
    setBrregData,
    brregFetchedAt,
    setBrregFetchedAt,
    useCustomAddress,
    setUseCustomAddress,
    lockedFields,
    setLockedFields,
    companySelected,
    setCompanySelected,

    // Contact details field state
    autoPopulatedFields,
    setAutoPopulatedFields,
    fieldOverrides,
    setFieldOverrides,

    // Helper functions
    resetState,
    validateStep,
    canNavigateToStep,
    getCompletionPercentage,
    isOnboardingComplete
  };
};
