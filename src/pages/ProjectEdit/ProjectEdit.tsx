import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, TextInput, TextArea, FormError, SubmitButton, PrimaryButton } from '../../components/ui';

const ProjectEdit: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();

  // State
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Mutations and queries
  const updateProject = useMutation(api.projects.updateProject);
  
  // Get project data
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Get user's access level for this project
  const userAccess = useQuery(
    api.teamManagement.validateUserProjectAccess,
    projectId && user?.id ? {
      clerkUserId: user.id,
      projectId: projectId as any,
    } : "skip"
  );

  // Load project data into form when available
  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        description: project.description || ''
      });
    }
  }, [project]);

  // Check if user has edit permissions
  const canEdit = userAccess?.hasAccess && (
    userAccess.accessLevel === "owner" || 
    userAccess.accessLevel === "administrator" || 
    userAccess.accessLevel === "collaborator" ||
    userAccess.accessLevel === "subcontractor"
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.id || !projectId) {
      setErrors({ general: 'Brukerinformasjon mangler' });
      return;
    }

    if (!canEdit) {
      setErrors({ general: 'Du har ikke tillatelse til å redigere dette prosjektet' });
      return;
    }

    // Validate form
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Prosjektnavn er påkrevd';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      setIsLoading(true);
      setErrors({});

      await updateProject({
        projectId: projectId as any,
        userId: user.id,
        name: formData.name.trim(),
        description: formData.description.trim()
      });

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate(`/project/${projectId}/details`); // Navigate back to project details
      }, 2000);

    } catch (error) {
      console.error('Error updating project:', error);
      setErrors({ 
        general: error instanceof Error ? error.message : 'Det oppstod en feil ved oppdatering av prosjektet. Prøv igjen.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state
  if (!project || userAccess === undefined) {
    return (
      <PageLayout
        showBackButton
        backUrl={`/project/${projectId}/details`}
        containerWidth="narrow"
        showFooter={false}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
            <p className="text-jobblogg-text-muted">Laster prosjektinformasjon...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Access denied
  if (!canEdit) {
    return (
      <PageLayout
        showBackButton
        backUrl={`/project/${projectId}/details`}
        containerWidth="narrow"
        showFooter={false}
      >
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <Heading2 className="mb-4">Ingen tilgang</Heading2>
          <p className="text-jobblogg-text-muted mb-6">
            Du har ikke tillatelse til å redigere dette prosjektet.
          </p>
          <PrimaryButton onClick={() => navigate(`/project/${projectId}/details`)}>
            Tilbake til prosjekt
          </PrimaryButton>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      showBackButton
      backUrl={`/project/${projectId}/details`}
      containerWidth="narrow"
      showFooter={false}
    >
      <div className="space-y-6">
        <div className="text-center">
          <Heading2>Rediger prosjekt</Heading2>
          <p className="text-jobblogg-text-muted mt-2">
            Oppdater prosjektinformasjon og beskrivelse
          </p>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="bg-jobblogg-success-soft border border-jobblogg-success/20 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <svg className="w-5 h-5 text-jobblogg-success mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="font-medium text-jobblogg-success">Prosjekt oppdatert!</span>
            </div>
            <p className="text-sm text-jobblogg-success">Sender deg tilbake til prosjektdetaljene...</p>
          </div>
        )}

        {/* Edit Form */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Project Name Field */}
            <TextInput
              label="Prosjektnavn"
              placeholder="F.eks. Kjøkkenrenovering, Terrasse bygging..."
              required
              fullWidth
              size="large"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              error={errors.name}
            />

            {/* Description Field */}
            <TextArea
              label="Beskrivelse"
              placeholder="Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer..."
              fullWidth
              rows={4}
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              helperText="💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere"
            />

            {/* General Error */}
            {errors.general && <FormError>{errors.general}</FormError>}

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <SubmitButton
                isLoading={isLoading}
                loadingText="Lagrer endringer..."
                className="flex-1"
              >
                Lagre endringer
              </SubmitButton>
              
              <PrimaryButton
                type="button"
                variant="secondary"
                onClick={() => navigate(`/project/${projectId}/details`)}
                className="flex-1"
              >
                Avbryt
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </PageLayout>
  );
};

export default ProjectEdit;
