import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, Heading3, BodyText, TextMuted, PrimaryButton, ArchiveActions, ArchiveStatusBadge, TextArea, ReadOnlyField, Accordion, FileUpload } from '../../components/ui';
import { ShareProjectModal } from '../../components/ShareProjectModal';
import { WithdrawalModal } from '../../components/WithdrawalModal';
import { ProjectAssignmentModal } from '../../components/team';
import { useUserRole } from '../../hooks/useUserRole';
import { getProjectPermissions } from '../../utils/projectPermissions';

// Job data interface
interface JobData {
  jobDescription: string;
  photos: Array<{
    url: string;
    note?: string;
    capturedAt?: number;
  }>;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  // personalNotes removed - now handled separately via userProjectNotes
}

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();
  const navigate = useNavigate();

  // User role information for team features
  const { isAdministrator, role: userRole } = useUserRole();

  // Job information state
  const [isJobSectionExpanded, setIsJobSectionExpanded] = useState(true);
  const [jobData, setJobData] = useState<JobData>({
    jobDescription: '',
    photos: [],
    accessNotes: '',
    equipmentNeeds: '',
    unresolvedQuestions: ''
    // personalNotes removed - now handled separately
  });

  // Separate state for user-specific personal notes
  const [personalNotes, setPersonalNotes] = useState<string>('');
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());
  const [isLocallyEditing, setIsLocallyEditing] = useState(false);

  // Modal states
  const [showShareModal, setShowShareModal] = useState(false);
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [showTeamAssignModal, setShowTeamAssignModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<{ url: string; note?: string } | null>(null);

  // Mutations
  const updateJobData = useMutation(api.projects.updateProjectJobData);
  const storeJobImage = useMutation(api.projects.storeJobImage);
  const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);

  // User-specific personal notes mutations and queries
  const updateUserProjectNotes = useMutation(api.userProjectNotes.updateUserProjectNotes);
  const userProjectNotes = useQuery(
    api.userProjectNotes.getUserProjectNotes,
    projectId && user?.id ? {
      projectId: projectId as any,
      userId: user.id,
    } : "skip"
  );

  // Get user's access level for this project
  const userAccess = useQuery(
    api.teamManagement.validateUserProjectAccess,
    projectId && user?.id ? {
      clerkUserId: user.id,
      projectId: projectId as any,
    } : "skip"
  );

  // Fetch project details with team access control
  const project = useQuery(
    api.projectsTeam.getByIdWithTeamAccess,
    projectId && user?.id ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );

  // Calculate user permissions for this project (after userAccess and project are defined)
  const projectPermissions = getProjectPermissions(
    userAccess as any,
    project?.userId === user?.id,
    userRole === 'utfoerende' ? 'utførende' : userRole as any
  );

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id && project ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );

  // Fetch shared project statistics
  const sharedStats = useQuery(
    api.projects.getProjectSharedStats,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Fetch job data with real-time sync
  const jobDataQuery = useQuery(
    api.projectsTeam.getJobDataForUser,
    projectId && user?.id ? {
      projectId: projectId as any,
      userId: user.id,
    } : "skip"
  );

  // Sync job data from query to local state
  useEffect(() => {
    if (jobDataQuery && !isLocallyEditing) {
      setJobData({
        jobDescription: jobDataQuery.jobDescription || '',
        photos: jobDataQuery.photos || [],
        accessNotes: jobDataQuery.accessNotes || '',
        equipmentNeeds: jobDataQuery.equipmentNeeds || '',
        unresolvedQuestions: jobDataQuery.unresolvedQuestions || '',
      });
    }
  }, [jobDataQuery, isLocallyEditing]);

  // Sync personal notes from query to local state
  useEffect(() => {
    if (userProjectNotes?.personalNotes !== undefined) {
      setPersonalNotes(userProjectNotes.personalNotes);
    }
  }, [userProjectNotes]);

  // Debounced save function for job data
  const debouncedSaveJobData = useCallback(() => {
    let timeoutId: NodeJS.Timeout;

    return (data: JobData) => {
      setIsLocallyEditing(true); // Mark as locally editing to prevent remote overwrites
      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        if (!projectId || !user?.id) return;

        try {
          setIsSaving(true);
          setSaveError(null);

          await updateJobData({
            projectId: projectId as any,
            userId: user.id,
            jobData: data
          });

          setIsLocallyEditing(false); // Allow remote updates again after successful save
        } catch (error) {
          console.error('Failed to save job data:', error);
          setSaveError('Kunne ikke lagre endringene. Prøv igjen.');
          setIsLocallyEditing(false);
        } finally {
          setIsSaving(false);
        }
      }, 1000); // 1 second debounce
    };
  }, [projectId, user?.id, updateJobData]);

  const saveJobData = debouncedSaveJobData();

  // Handle job data changes
  const handleJobDataChange = (field: keyof JobData, value: string) => {
    const newData = { ...jobData, [field]: value };
    setJobData(newData);
    saveJobData(newData);
  };

  // Debounced save function for personal notes
  const debouncedSavePersonalNotes = useCallback(() => {
    let timeoutId: NodeJS.Timeout;

    return (notes: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        if (!projectId || !user?.id) return;

        try {
          await updateUserProjectNotes({
            projectId: projectId as any,
            userId: user.id,
            personalNotes: notes
          });
        } catch (error) {
          console.error('Failed to save personal notes:', error);
        }
      }, 1000); // 1 second debounce
    };
  }, [projectId, user?.id, updateUserProjectNotes]);

  const savePersonalNotes = debouncedSavePersonalNotes();

  // Handle personal notes changes
  const handlePersonalNotesChange = (notes: string) => {
    setPersonalNotes(notes);
    savePersonalNotes(notes);
  };

  // Handle image upload
  const handleImageUpload = async (files: File[]) => {
    if (!files.length || !projectId || !user?.id) return;

    const newUploadingImages = new Set(uploadingImages);

    for (const file of files) {
      const fileId = `${file.name}-${Date.now()}`;
      newUploadingImages.add(fileId);

      try {
        // Generate upload URL
        const uploadUrl = await generateUploadUrl();

        // Upload file
        const result = await fetch(uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': file.type },
          body: file,
        });

        if (!result.ok) throw new Error('Upload failed');

        const { storageId } = await result.json();

        // Store image in project
        await storeJobImage({
          projectId: projectId as any,
          userId: user.id,
          storageId
        });

        newUploadingImages.delete(fileId);
        setUploadingImages(new Set(newUploadingImages));

      } catch (error) {
        console.error('Failed to upload image:', error);
        newUploadingImages.delete(fileId);
        setUploadingImages(new Set(newUploadingImages));
      }
    }
  };

  // Handle photo note update
  const handlePhotoNoteUpdate = (index: number, note: string) => {
    const updatedPhotos = [...jobData.photos];
    updatedPhotos[index] = { ...updatedPhotos[index], note };
    const newData = { ...jobData, photos: updatedPhotos };
    setJobData(newData);
    saveJobData(newData);
  };

  // Handle photo deletion
  const handlePhotoDelete = (index: number) => {
    const updatedPhotos = jobData.photos.filter((_, i) => i !== index);
    const newData = { ...jobData, photos: updatedPhotos };
    setJobData(newData);
    saveJobData(newData);
  };

  // Loading state
  if (!project || !user) {
    return (
      <PageLayout
        showBackButton
        backUrl="/"
        containerWidth="wide"
        showFooter={false}
      >
        <div className="animate-pulse space-y-8">
          {/* Header Skeleton */}
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-jobblogg-neutral rounded-full"></div>
            <div className="h-8 bg-jobblogg-neutral rounded w-64 mx-auto"></div>
          </div>

          {/* Project Info Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="bg-base-100 rounded-xl p-8 shadow-lg">
                <div className="skeleton h-8 w-48 mb-4"></div>
                <div className="skeleton h-20 w-full mb-6"></div>
                <div className="flex gap-4">
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                </div>
              </div>
            </div>
            <div>
              <div className="bg-base-100 rounded-xl p-6 shadow-lg">
                <div className="skeleton h-6 w-24 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-3/4"></div>
                  <div className="skeleton h-4 w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Gallery Skeleton */}
          <div className="bg-white rounded-xl p-8 shadow-lg border border-jobblogg-border">
            <div className="skeleton h-8 w-32 mb-6"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="skeleton h-48 w-full rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      showBackButton
      backUrl="/"
      containerWidth="wide"
      showFooter={false}
      headerActions={
        <div className="flex items-center gap-2">
          <ArchiveStatusBadge
            isArchived={project.isArchived}
            archivedAt={project.archivedAt}
          />
          <div className="flex items-center gap-2 px-3 py-1.5 bg-jobblogg-primary-soft text-jobblogg-primary rounded-full text-sm font-medium min-h-[44px]">
            <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="hidden sm:inline whitespace-nowrap">Prosjektdetaljer</span>
            <span className="sm:hidden">Detaljer</span>
          </div>
        </div>
      }
    >
      <div className="space-y-6 sm:space-y-8">
        {/* Project Header */}
        <div className="text-center space-y-3 sm:space-y-4 animate-slide-up">
          <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 sm:w-8 sm:h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-jobblogg-text-strong leading-tight break-words px-2 sm:px-0">
            {project.name}
          </h1>
        </div>

        {/* Two-Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-10 gap-6 lg:gap-8">
          {/* Main Content Column (70% - 7/10 columns) */}
          <div className="lg:col-span-7 space-y-6">
            {/* Job Information Section with Accordions */}
            <div className="space-y-4">
              <Heading2>Jobbinformasjon</Heading2>

              {/* Job Description Accordion */}
              <Accordion
                title="Detaljerte arbeidsinstruksjoner"
                defaultExpanded={isJobSectionExpanded}
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                }
              >
                {projectPermissions.canEditProject ? (
                  <TextArea
                    placeholder="Beskriv detaljerte instruksjoner for arbeidet som skal utføres..."
                    value={jobData.jobDescription}
                    onChange={(e) => handleJobDataChange('jobDescription', e.target.value)}
                    rows={4}
                    className="w-full"
                  />
                ) : (
                  <ReadOnlyField
                    label=""
                    value={jobData.jobDescription || 'Ingen detaljerte instruksjoner tilgjengelig'}
                    className="min-h-[100px]"
                  />
                )}
              </Accordion>

              {/* Access Notes Accordion */}
              <Accordion
                title="Tilkomst og forhold"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                  </svg>
                }
              >
                {projectPermissions.canEditProject ? (
                  <TextArea
                    placeholder="Beskriv tilkomst, parkeringsmuligheter, nøkler, koder, etc..."
                    value={jobData.accessNotes}
                    onChange={(e) => handleJobDataChange('accessNotes', e.target.value)}
                    rows={3}
                    className="w-full"
                  />
                ) : (
                  <ReadOnlyField
                    label=""
                    value={jobData.accessNotes || 'Ingen tilkomstinformasjon tilgjengelig'}
                  />
                )}
              </Accordion>

              {/* Equipment Needs Accordion */}
              <Accordion
                title="Hva må medbringes?"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                }
              >
                {projectPermissions.canEditProject ? (
                  <TextArea
                    placeholder="List opp verktøy, materialer og utstyr som må medbringes..."
                    value={jobData.equipmentNeeds}
                    onChange={(e) => handleJobDataChange('equipmentNeeds', e.target.value)}
                    rows={3}
                    className="w-full"
                  />
                ) : (
                  <ReadOnlyField
                    label=""
                    value={jobData.equipmentNeeds || 'Ingen utstyrsbehov spesifisert'}
                  />
                )}
              </Accordion>

              {/* Unresolved Questions Accordion */}
              <Accordion
                title="Hva må avklares?"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
              >
                {projectPermissions.canEditProject ? (
                  <TextArea
                    placeholder="Noter spørsmål som må avklares før eller under arbeidet..."
                    value={jobData.unresolvedQuestions}
                    onChange={(e) => handleJobDataChange('unresolvedQuestions', e.target.value)}
                    rows={3}
                    className="w-full"
                  />
                ) : (
                  <ReadOnlyField
                    label=""
                    value={jobData.unresolvedQuestions || 'Ingen uløste spørsmål'}
                  />
                )}
              </Accordion>
            </div>

            {/* Image Gallery Section */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
                <Heading2 className="min-w-0 flex-shrink">Bilder fra befaring</Heading2>
                {projectPermissions.canEditProject && (
                  <PrimaryButton
                    onClick={() => setShowImageUpload(!showImageUpload)}
                    className="min-h-[44px] w-full sm:w-auto flex-shrink-0"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Last opp bilder
                  </PrimaryButton>
                )}
              </div>

              {/* Image Upload Interface */}
              {showImageUpload && projectPermissions.canEditProject && (
                <div className="border-t border-jobblogg-border pt-6">
                  <FileUpload
                    label="Last opp bilder"
                    helperText="Velg bilder fra befaring eller prosjektarbeid. JPEG, PNG eller WebP • Maks 10MB per bilde"
                    accept=".jpg,.jpeg,.png,.webp"
                    maxSize={10 * 1024 * 1024} // 10MB
                    multiple
                    maxFiles={10}
                    files={[]} // We'll handle this differently since we're uploading directly
                    onFilesChange={handleImageUpload}
                    error={saveError || undefined}
                  />
                </div>
              )}

              {/* Photo Grid */}
              {jobData.photos.length > 0 && (
                <div
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
                  role="grid"
                  aria-label="Bilder fra befaring"
                >
                  {jobData.photos.map((photo, index) => (
                    <div
                      key={index}
                      className="relative bg-jobblogg-neutral rounded-lg overflow-hidden border border-jobblogg-border"
                      role="gridcell"
                    >
                      <div className="aspect-square relative">
                        <img
                          src={photo.url}
                          alt={photo.note ? `Befaring bilde ${index + 1}: ${photo.note}` : `Befaring bilde ${index + 1}`}
                          className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                          loading="lazy"
                          onClick={() => setSelectedImage({ url: photo.url, note: photo.note })}
                        />
                        <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                          {index + 1}
                        </div>
                      </div>
                      {photo.note && (
                        <div className="p-3 bg-white">
                          <p className="text-sm text-jobblogg-text-muted line-clamp-2">{photo.note}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {jobData.photos.length === 0 && (
                <div className="text-center py-12 text-jobblogg-text-muted">
                  <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
                  </svg>
                  <p>Ingen bilder lastet opp ennå</p>
                </div>
              )}
            </div>

            {/* Team og Aktivitet Section */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
              <div className="flex items-center justify-between mb-6">
                <Heading2>Team og Aktivitet</Heading2>
                {projectPermissions.canAssignTeamMembers && (
                  <PrimaryButton
                    onClick={() => setShowTeamAssignModal(true)}
                    className="min-h-[44px]"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Legg til teammedlem
                  </PrimaryButton>
                )}
              </div>

              {/* Team Members Subsection */}
              <div className="space-y-4">
                <div>
                  <Heading3 className="text-base font-medium text-jobblogg-text-strong mb-3">Prosjekteier</Heading3>
                  <div className="flex items-center gap-3 p-3 bg-jobblogg-neutral rounded-lg border border-jobblogg-border">
                    <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium text-sm text-jobblogg-text-strong">Administrator</div>
                      <div className="text-xs text-jobblogg-text-muted">Prosjekteier</div>
                    </div>
                  </div>
                </div>

                <div>
                  <Heading3 className="text-base font-medium text-jobblogg-text-strong mb-3">Tildelte teammedlemmer (0)</Heading3>
                  <div className="text-center py-8 text-jobblogg-text-muted">
                    <p className="text-sm">Ingen teammedlemmer er tildelt ennå.</p>
                  </div>
                </div>

                {/* Activity Log Subsection */}
                <div className="border-t border-jobblogg-border pt-6">
                  <Heading3 className="text-base font-medium text-jobblogg-text-strong mb-3">Nylig aktivitet</Heading3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 bg-jobblogg-neutral rounded-lg border border-jobblogg-border">
                      <div className="w-6 h-6 bg-jobblogg-success-soft rounded-full flex items-center justify-center mt-0.5">
                        <svg className="w-3 h-3 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-jobblogg-text-medium">
                          <span className="font-medium">Prosjekt opprettet</span>
                        </p>
                        <p className="text-xs text-jobblogg-text-muted mt-1">
                          {new Date(project._creationTime).toLocaleDateString('nb-NO', {
                            day: 'numeric',
                            month: 'long',
                            year: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar Column (30% - 3/10 columns) */}
          <div className="lg:col-span-3 space-y-6">
            {/* Primary Actions */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
              <div className="flex flex-col gap-3">
                {/* Share Project Button */}
                {projectPermissions.canShare && (
                  <PrimaryButton
                    onClick={() => setShowShareModal(true)}
                    className="w-full min-h-[44px]"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    Del prosjekt
                  </PrimaryButton>
                )}

                {/* Edit Project Button */}
                {projectPermissions.canEditProject && (
                  <PrimaryButton
                    onClick={() => navigate(`/project/${projectId}/edit`)}
                    className="w-full min-h-[44px]"
                    variant="secondary"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Rediger
                  </PrimaryButton>
                )}

                {/* Archive Actions */}
                {projectPermissions.canArchive && (
                  <div className="pt-2 border-t border-jobblogg-border">
                    <ArchiveActions
                      projectId={projectId!}
                      isArchived={project?.isArchived}
                      onArchiveComplete={() => navigate('/', { replace: true })}
                      onRestoreComplete={() => navigate('/', { replace: true })}
                      className="min-h-[44px] w-full"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Project Information Card */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
              <Heading3>Prosjektinformasjon</Heading3>

              {/* Project Summary */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <TextMuted className="text-sm font-medium">Sammendrag</TextMuted>
                </div>
                <div className="bg-jobblogg-neutral rounded-lg p-3 border border-jobblogg-border">
                  <BodyText className="text-sm leading-relaxed">
                    {project.description || (
                      <TextMuted as="span" className="italic text-xs">
                        Ingen sammendrag tilgjengelig
                      </TextMuted>
                    )}
                  </BodyText>
                </div>
              </div>

              {/* Customer Information */}
              {project.customer && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <TextMuted className="text-sm font-medium">Kunde</TextMuted>
                  </div>
                  <div className="bg-jobblogg-neutral rounded-lg p-3 border border-jobblogg-border space-y-2">
                    <div className="font-medium text-sm">{project.customer.name}</div>
                    {project.customer.contactPerson && (
                      <div className="text-xs text-jobblogg-text-muted">
                        Kontakt: {project.customer.contactPerson}
                      </div>
                    )}
                    {project.customer.phone && (
                      <div className="text-xs text-jobblogg-text-muted">
                        Tlf: {project.customer.phone}
                      </div>
                    )}
                    {project.customer.email && (
                      <div className="text-xs text-jobblogg-text-muted">
                        E-post: {project.customer.email}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Map Link */}
              {project.customer?.address && (
                <div>
                  <PrimaryButton
                    onClick={() => {
                      const address = `${project.customer.address}, ${project.customer.postalCode} ${project.customer.postalArea}`;
                      const encodedAddress = encodeURIComponent(address);
                      window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
                    }}
                    className="w-full min-h-[44px]"
                    variant="secondary"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Åpne kart
                  </PrimaryButton>
                </div>
              )}
            </div>

            {/* Statistics Card */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
              <Heading3>Statistikk</Heading3>

              <div className="space-y-3">
                {/* Total Images */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
                    </svg>
                    <TextMuted className="text-sm">Bilder</TextMuted>
                  </div>
                  <div className="font-semibold text-jobblogg-primary text-right">
                    {jobData.photos.length}
                  </div>
                </div>

                {/* Shared Stats */}
                {sharedStats && (
                  <>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <TextMuted className="text-sm">Visninger</TextMuted>
                      </div>
                      <div className="font-semibold text-jobblogg-success text-right">
                        {sharedStats.accessCount}
                      </div>
                    </div>

                    {sharedStats.lastAccessedAt && (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <TextMuted className="text-sm">Sist åpnet</TextMuted>
                        </div>
                        <div className="text-xs text-jobblogg-text-muted text-right">
                          {new Date(sharedStats.lastAccessedAt).toLocaleDateString('nb-NO')}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Modals */}
        {showShareModal && (
          <ShareProjectModal
            isOpen={showShareModal}
            project={{
              _id: project._id,
              name: project.name,
              sharedId: project.sharedId,
              isPubliclyShared: project.isPubliclyShared,
              shareSettings: project.shareSettings
            }}
            userId={user.id}
            onClose={() => setShowShareModal(false)}
          />
        )}

        {showWithdrawalModal && (
          <WithdrawalModal
            isOpen={showWithdrawalModal}
            projectId={projectId!}
            projectName={project.name}
            userId={user.id}
            onClose={() => setShowWithdrawalModal(false)}
            onWithdrawalComplete={() => navigate('/', { replace: true })}
          />
        )}

        {/* Team Assignment Modal */}
        <ProjectAssignmentModal
          isOpen={showTeamAssignModal}
          projectId={projectId!}
          projectName={project?.name || ''}
          onClose={() => setShowTeamAssignModal(false)}
          onSuccess={() => {
            setShowTeamAssignModal(false);
            // The team data will automatically refresh via Convex reactivity
          }}
        />
      </div>
    </PageLayout>
  );
};

export default ProjectDetail;