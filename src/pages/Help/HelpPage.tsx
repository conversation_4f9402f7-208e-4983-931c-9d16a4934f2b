import React, { useState } from 'react';
import { PageLayout, Heading1, Heading2, TextMedium, TextMuted, PrimaryButton } from '../../components/ui';
import { SEOHead } from '../../components/SEO';
import { Link } from 'react-router-dom';

export const HelpPage: React.FC = () => {
  const [openFaqSection, setOpenFaqSection] = useState<string | null>(null);

  const toggleFaqSection = (section: string) => {
    setOpenFaqSection(openFaqSection === section ? null : section);
  };

  return (
    <>
      <SEOHead 
        title="Hjelp og FAQ - JobbLogg"
        description="Få svar på vanlige spørsmål om JobbLogg. Lær hvordan du dokumenterer prosjekter, kommuniserer med kunder og administrerer teamet ditt."
        keywords="hjelp, FAQ, support, dokumentasjon, prosjektdokumentasjon, håndverker, byggebransje"
        url="/help"
      />
      
      <PageLayout showFooter={true}>
        <div className="max-w-4xl mx-auto space-y-12">
          {/* Header */}
          <div className="text-center space-y-6">
            <Heading1>Hjelp og FAQ</Heading1>
            <TextMedium className="text-lg max-w-2xl mx-auto text-jobblogg-text-medium">
              Få svar på vanlige spørsmål om JobbLogg og lær hvordan du får mest mulig ut av plattformen.
            </TextMedium>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4">
              <div className="w-12 h-12 bg-jobblogg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-strong mb-2">Kom i gang</h3>
                <TextMuted className="text-sm">Lær grunnleggende om hvordan JobbLogg fungerer</TextMuted>
              </div>
            </div>

            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4">
              <div className="w-12 h-12 bg-jobblogg-success/10 rounded-xl flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-strong mb-2">Kontakt support</h3>
                <TextMuted className="text-sm">Send e-<NAME_EMAIL></TextMuted>
              </div>
            </div>

            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4">
              <div className="w-12 h-12 bg-jobblogg-accent/10 rounded-xl flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-strong mb-2">Systemstatus</h3>
                <TextMuted className="text-sm">Alle systemer operative</TextMuted>
              </div>
            </div>
          </div>

          {/* FAQ Sections */}
          <div className="space-y-4">
            <Heading2 className="text-center mb-8">Vanlige spørsmål</Heading2>
            
            {/* Komme i gang */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft overflow-hidden">
              <button
                onClick={() => toggleFaqSection('getting-started')}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-jobblogg-card-bg transition-colors duration-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-jobblogg-primary/10 rounded-lg flex items-center justify-center">
                    <span className="text-jobblogg-primary text-lg">🚀</span>
                  </div>
                  <h3 className="font-semibold text-jobblogg-text-strong">Komme i gang</h3>
                </div>
                <svg 
                  className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
                    openFaqSection === 'getting-started' ? 'rotate-180' : ''
                  }`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFaqSection === 'getting-started' && (
                <div className="px-6 pb-4 space-y-4 border-t border-jobblogg-border">
                  <div className="pt-4">
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan registrerer jeg bedriften min?</h4>
                    <TextMuted className="text-sm mb-3">
                      Opprett konto med e-post. Bedriftsinformasjon hentes automatisk fra Brønnøysundregisteret når du oppgir organisasjonsnummer.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan oppretter jeg mitt første prosjekt?</h4>
                    <TextMuted className="text-sm mb-3">
                      Klikk på "Opprett prosjekt" fra dashbordet. Fyll inn prosjektinformasjon, kundeopplysninger og prosjektdetaljer. Du kan også bruke prosjektveiviseren for en guidet opprettelse.
                    </TextMuted>
                  </div>

                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvor mange prosjekter kan jeg ha samtidig?</h4>
                    <TextMuted className="text-sm">
                      Det er ingen begrensning på antall aktive prosjekter. Du kan ha så mange som du trenger.
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>

            {/* Prosjektdokumentasjon */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft overflow-hidden">
              <button
                onClick={() => toggleFaqSection('documentation')}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-jobblogg-card-bg transition-colors duration-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-jobblogg-success/10 rounded-lg flex items-center justify-center">
                    <span className="text-jobblogg-success text-lg">📝</span>
                  </div>
                  <h3 className="font-semibold text-jobblogg-text-strong">Prosjektdokumentasjon</h3>
                </div>
                <svg 
                  className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
                    openFaqSection === 'documentation' ? 'rotate-180' : ''
                  }`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFaqSection === 'documentation' && (
                <div className="px-6 pb-4 space-y-4 border-t border-jobblogg-border">
                  <div className="pt-4">
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan dokumenterer jeg arbeidsframgang?</h4>
                    <TextMuted className="text-sm mb-3">
                      Gå til prosjektsiden og klikk "Legg til oppføring". Ta bilder direkte med kameraet eller last opp eksisterende bilder. Skriv en beskrivelse av arbeidet som er utført.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvilke filtyper kan jeg laste opp?</h4>
                    <TextMuted className="text-sm mb-3">
                      Du kan laste opp bilder i formatene JPEG, PNG og WebP. Maksimal filstørrelse er 10 MB per bilde.
                    </TextMuted>
                  </div>

                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan organiserer jeg oppføringer kronologisk?</h4>
                    <TextMuted className="text-sm">
                      Oppføringer vises automatisk i kronologisk rekkefølge med nyeste øverst. Du kan også filtrere på dato for å finne spesifikke oppføringer.
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>

            {/* Kunde og team */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft overflow-hidden">
              <button
                onClick={() => toggleFaqSection('customer-team')}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-jobblogg-card-bg transition-colors duration-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-jobblogg-accent/10 rounded-lg flex items-center justify-center">
                    <span className="text-jobblogg-accent text-lg">👥</span>
                  </div>
                  <h3 className="font-semibold text-jobblogg-text-strong">Kunde og team</h3>
                </div>
                <svg
                  className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
                    openFaqSection === 'customer-team' ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFaqSection === 'customer-team' && (
                <div className="px-6 pb-4 space-y-4 border-t border-jobblogg-border">
                  <div className="pt-4">
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan inviterer jeg kunden til å følge prosjektet?</h4>
                    <TextMuted className="text-sm mb-3">
                      Kunder mottar automatisk e-postinvitasjoner når de legges til i prosjekter. Du kan også manuelt dele prosjektlenker ved å gå til prosjektsiden og klikke "Del prosjekt". Kunden trenger ikke å laste ned noen app.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Kan jeg legge til andre ansatte i bedriften?</h4>
                    <TextMuted className="text-sm mb-3">
                      Ja, gå til "Team" i hovedmenyen og inviter team-medlemmer. Du kan gi dem forskjellige roller: Administrator eller Utførende.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hva ser kunden når de får tilgang til prosjektet?</h4>
                    <TextMuted className="text-sm mb-3">
                      Kunden ser alle prosjektoppføringer med bilder og beskrivelser, kan chatte med deg, men kan ikke endre eller slette noe.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan fungerer chat med kunder?</h4>
                    <TextMuted className="text-sm mb-3">
                      Chat er tilgjengelig på alle delte prosjekter. Både du og kunden kan sende meldinger, bilder og stille spørsmål i sanntid.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Kan jeg samarbeide med underentreprenører?</h4>
                    <TextMuted className="text-sm">
                      Ja, du kan invitere underentreprenører til spesifikke prosjekter. De får tilgang til å dokumentere sitt arbeid og kommunisere med deg.
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>

            {/* Teknisk */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft overflow-hidden">
              <button
                onClick={() => toggleFaqSection('technical')}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-jobblogg-card-bg transition-colors duration-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-jobblogg-warning/10 rounded-lg flex items-center justify-center">
                    <span className="text-jobblogg-warning text-lg">📱</span>
                  </div>
                  <h3 className="font-semibold text-jobblogg-text-strong">Teknisk</h3>
                </div>
                <svg
                  className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
                    openFaqSection === 'technical' ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFaqSection === 'technical' && (
                <div className="px-6 pb-4 space-y-4 border-t border-jobblogg-border">
                  <div className="pt-4">
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvilke enheter fungerer med JobbLogg?</h4>
                    <TextMuted className="text-sm mb-3">
                      JobbLogg fungerer på alle moderne mobiler, nettbrett og datamaskiner. Både iOS, Android og web-nettlesere støttes.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvor lagres bildene og dataene mine?</h4>
                    <TextMuted className="text-sm mb-3">
                      Alt lagres sikkert i Norge med automatisk backup. Dataene er kryptert og følger GDPR-reglene for personvern.
                    </TextMuted>
                  </div>


                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvordan sikrer dere mine data?</h4>
                    <TextMuted className="text-sm">
                      Vi bruker industri-standard kryptering, regelmessige sikkerhetskopier og følger norske personvernlover. Dataene lagres i Norge.
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>

            {/* Priser og abonnement */}
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft overflow-hidden">
              <button
                onClick={() => toggleFaqSection('pricing')}
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-jobblogg-card-bg transition-colors duration-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-jobblogg-primary/10 rounded-lg flex items-center justify-center">
                    <span className="text-jobblogg-primary text-lg">💰</span>
                  </div>
                  <h3 className="font-semibold text-jobblogg-text-strong">Priser og abonnement</h3>
                </div>
                <svg
                  className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
                    openFaqSection === 'pricing' ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFaqSection === 'pricing' && (
                <div className="px-6 pb-4 space-y-4 border-t border-jobblogg-border">
                  <div className="pt-4">
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hvor mye koster JobbLogg?</h4>
                    <TextMuted className="text-sm mb-3">
                      Vi har fleksible priser basert på antall prosjekter og team-medlemmer. Start gratis i 30 dager, deretter fra 299 kr/måned.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Kan jeg avslutte abonnementet når som helst?</h4>
                    <TextMuted className="text-sm mb-3">
                      Ja, det er ingen binding. Du kan avslutte når som helst og beholder tilgang til dataene dine.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Hva skjer med dataene mine hvis jeg avslutter?</h4>
                    <TextMuted className="text-sm mb-3">
                      Du kan eksportere alle data før avslutning. Vi sletter dataene etter 90 dager som avtalt i brukervilkårene.
                    </TextMuted>
                  </div>
                  <div>
                    <h4 className="font-medium text-jobblogg-text-strong mb-2">Finnes det rabatt for større team?</h4>
                    <TextMuted className="text-sm">
                      Ja, vi tilbyr volumrabatter for bedrifter med mange team-medlemmer. Kontakt oss for tilpasset pris.
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gradient-to-br from-jobblogg-primary/5 to-jobblogg-accent/5 rounded-2xl p-8 text-center">
            <Heading2 className="mb-4">Fant du ikke svaret?</Heading2>
            <TextMuted className="mb-6 max-w-2xl mx-auto">
              Vårt supportteam er klare til å hjelpe deg. Send oss en e-post så svarer vi så raskt som mulig.
            </TextMuted>
            <a href="mailto:<EMAIL>">
              <PrimaryButton className="px-8 py-3">
                Kontakt support
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </PrimaryButton>
            </a>
          </div>
        </div>
      </PageLayout>
    </>
  );
};

export default HelpPage;
