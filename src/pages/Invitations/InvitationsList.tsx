import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { getSpecializationById } from '../../utils/specializations';
import {
  DashboardLayout,
  Heading2,
  BodyText,
  TextMuted,
  PrimaryButton,
  SecondaryButton,
  EmptyState,
  StatsCard
} from '../../components/ui';

const InvitationsList: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const [statusFilter, setStatusFilter] = useState<'pending' | 'all'>('pending');

  // Query invitations
  const invitations = useQuery(
    api.subcontractorInvitations.getSubcontractorInvitations,
    user?.id ? {
      userId: user.id,
      status: statusFilter,
    } : "skip"
  );

  // Query invitation statistics
  const invitationStats = useQuery(
    api.subcontractorInvitations.getInvitationStats,
    user?.id ? { userId: user.id } : "skip"
  );

  const isLoading = invitations === undefined || invitationStats === undefined;

  // No need to filter again - backend already filters by status
  // When statusFilter is 'pending', invitations already contains only pending invitations
  // When statusFilter is 'all', invitations contains all invitations
  const pendingInvitations = statusFilter === 'pending' ? (invitations || []) : (invitations?.filter(inv => inv.invitationStatus === 'pending') || []);
  const respondedInvitations = invitations?.filter(inv => inv.invitationStatus !== 'pending') || [];

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatUrgency = (urgency?: string) => {
    switch (urgency) {
      case 'high': return { text: 'Høy prioritet', color: 'text-jobblogg-error' };
      case 'medium': return { text: 'Middels prioritet', color: 'text-jobblogg-warning' };
      case 'low': return { text: 'Lav prioritet', color: 'text-jobblogg-success' };
      default: return { text: 'Ikke oppgitt', color: 'text-jobblogg-text-medium' };
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-warning/10 text-jobblogg-warning border border-jobblogg-warning/20">Venter på svar</span>;
      case 'accepted':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20">Godtatt</span>;
      case 'declined':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-error/10 text-jobblogg-error border border-jobblogg-error/20">Avslått</span>;
      case 'expired':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-text-medium/10 text-jobblogg-text-medium border border-jobblogg-text-medium/20">Utløpt</span>;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout title="Laster invitasjoner..." subtitle="Henter dine prosjektinvitasjoner...">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Prosjektinvitasjoner"
      subtitle="Administrer dine invitasjoner til underleverandørprosjekter"
      headerActions={
        <div className="flex items-center gap-3">
          <SecondaryButton
            onClick={() => navigate('/')}
            className="flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Tilbake til oversikt
          </SecondaryButton>
        </div>
      }
      statsSection={
        <div className="grid-stats">
          <StatsCard
            title="Ventende invitasjoner"
            value={invitationStats?.pendingCount || 0}
            variant="warning"
            animationDelay="0s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            }
          />
          <StatsCard
            title="Godtatte prosjekter"
            value={invitationStats?.acceptedCount || 0}
            variant="success"
            animationDelay="0.1s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
          <StatsCard
            title="Avslåtte invitasjoner"
            value={invitationStats?.declinedCount || 0}
            variant="error"
            animationDelay="0.2s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
          <StatsCard
            title="Totalt invitasjoner"
            value={invitationStats?.totalCount || 0}
            variant="primary"
            animationDelay="0.3s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            }
          />
        </div>
      }
    >
      <div className="space-y-8">
        {/* Filter Tabs */}
        <section className="space-y-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setStatusFilter('pending')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                statusFilter === 'pending'
                  ? 'bg-jobblogg-primary text-white'
                  : 'bg-jobblogg-neutral text-jobblogg-text-medium hover:bg-jobblogg-neutral-hover'
              }`}
            >
              Ventende ({invitationStats?.pendingCount || 0})
            </button>
            <button
              onClick={() => setStatusFilter('all')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                statusFilter === 'all'
                  ? 'bg-jobblogg-primary text-white'
                  : 'bg-jobblogg-neutral text-jobblogg-text-medium hover:bg-jobblogg-neutral-hover'
              }`}
            >
              Alle invitasjoner ({invitationStats?.totalCount || 0})
            </button>
          </div>
        </section>

        {/* Pending Invitations */}
        {statusFilter === 'pending' && (
          <section className="space-y-6">
            <Heading2 className="flex items-center gap-3">
              <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Ventende invitasjoner
            </Heading2>

            {pendingInvitations.length === 0 ? (
              <EmptyState
                title="🎉 Ingen ventende invitasjoner"
                description="Du har ingen ventende prosjektinvitasjoner for øyeblikket. Nye invitasjoner vil vises her."
                actionLabel="Se alle invitasjoner"
                onAction={() => setStatusFilter('all')}
              />
            ) : (
              <div className="space-y-4">
                {pendingInvitations.map((invitation, index) => (
                  <div
                    key={invitation._id}
                    className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 hover:shadow-medium transition-shadow duration-200 animate-slide-up"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="text-lg font-semibold text-jobblogg-text-strong">
                                {invitation.projectPreview?.name || 'Prosjekt'}
                              </h3>
                              {invitation.invitationDirection === 'outgoing' && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                                  Sendt
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-jobblogg-text-medium">
                              {invitation.invitationDirection === 'outgoing'
                                ? `Sendt til ${invitation.subcontractorInfo?.company?.name || 'Ukjent firma'} (${invitation.subcontractorInfo?.contactPersonName || 'Ukjent kontakt'}) • ${getSpecializationById(invitation.subcontractorSpecialization)?.name || invitation.subcontractorSpecialization}`
                                : `${invitation.projectPreview?.mainContractorCompany} • ${getSpecializationById(invitation.subcontractorSpecialization)?.name || invitation.subcontractorSpecialization}`
                              }
                            </p>
                          </div>
                          {getStatusBadge(invitation.invitationStatus || 'pending')}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                              </svg>
                              <span>{invitation.projectPreview?.address}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              <span>Invitert av {invitation.projectPreview?.inviterName}</span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
                              </svg>
                              <span>{invitation.estimatedDuration || 'Ikke oppgitt'}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span className={formatUrgency(invitation.urgency).color}>
                                {formatUrgency(invitation.urgency).text}
                              </span>
                            </div>
                          </div>
                        </div>

                        {invitation.invitationMessage && (
                          <div className="bg-jobblogg-neutral p-3 rounded-lg">
                            <p className="text-sm text-jobblogg-text-medium">
                              <strong>Melding:</strong> {invitation.invitationMessage}
                            </p>
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-2">
                          <TextMuted className="text-xs">
                            Invitert {formatDate(invitation.invitedAt || invitation._creationTime)}
                            {invitation.expiresAt && (
                              <span className="text-jobblogg-error ml-2">
                                • Utløper {formatDate(invitation.expiresAt)}
                              </span>
                            )}
                          </TextMuted>
                          <div className="flex items-center gap-2">
                            <PrimaryButton
                              size="sm"
                              onClick={() => navigate(`/invitations/${invitation._id}`)}
                            >
                              Se detaljer
                            </PrimaryButton>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </section>
        )}

        {/* All Invitations */}
        {statusFilter === 'all' && (
          <section className="space-y-6">
            <Heading2>Alle invitasjoner</Heading2>

            {(invitations?.length || 0) === 0 ? (
              <EmptyState
                title="📬 Ingen invitasjoner ennå"
                description="Du har ikke mottatt noen prosjektinvitasjoner ennå. Når hovedentreprenører inviterer deg til prosjekter, vil de vises her."
                actionLabel="Gå til oversikt"
                onAction={() => navigate('/')}
              />
            ) : (
              <div className="space-y-4">
                {invitations?.map((invitation, index) => (
                  <div
                    key={invitation._id}
                    className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 hover:shadow-medium transition-shadow duration-200 animate-slide-up"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="text-lg font-semibold text-jobblogg-text-strong">
                                {invitation.projectPreview?.name || 'Prosjekt'}
                              </h3>
                              {invitation.invitationDirection === 'outgoing' && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                                  Sendt
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-jobblogg-text-medium">
                              {invitation.invitationDirection === 'outgoing'
                                ? `Sendt til ${invitation.subcontractorInfo?.company?.name || 'Ukjent firma'} (${invitation.subcontractorInfo?.contactPersonName || 'Ukjent kontakt'}) • ${getSpecializationById(invitation.subcontractorSpecialization)?.name || invitation.subcontractorSpecialization}`
                                : `${invitation.projectPreview?.mainContractorCompany} • ${getSpecializationById(invitation.subcontractorSpecialization)?.name || invitation.subcontractorSpecialization}`
                              }
                            </p>
                          </div>
                          {getStatusBadge(invitation.invitationStatus || 'pending')}
                        </div>

                        <div className="flex items-center justify-between pt-2">
                          <TextMuted className="text-xs">
                            Invitert {formatDate(invitation.invitedAt || invitation._creationTime)}
                            {invitation.respondedAt && (
                              <span className="ml-2">
                                • Besvart {formatDate(invitation.respondedAt)}
                              </span>
                            )}
                          </TextMuted>
                          <div className="flex items-center gap-2">
                            <SecondaryButton
                              size="sm"
                              onClick={() => navigate(`/invitations/${invitation._id}`)}
                            >
                              Se detaljer
                            </SecondaryButton>
                            {invitation.invitationStatus === 'accepted' && (
                              <PrimaryButton
                                size="sm"
                                onClick={() => navigate(`/project/${invitation.projectId}`)}
                              >
                                Åpne prosjekt
                              </PrimaryButton>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </section>
        )}
      </div>
    </DashboardLayout>
  );
};

export default InvitationsList;
