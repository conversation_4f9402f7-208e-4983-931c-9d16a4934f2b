import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { PageLayout, Heading2, TextMedium, TextMuted, PrimaryButton, SecondaryButton } from '../../components/ui';
import { CookieSettings } from '../../components/CookieConsent';

export const PrivacySettings: React.FC = () => {
  const { user } = useUser();
  const [isExporting, setIsExporting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleExportData = async () => {
    setIsExporting(true);
    try {
      // This would call a Convex function to export user data
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would download a JSON file
      const userData = {
        user: {
          id: user?.id,
          email: user?.primaryEmailAddress?.emailAddress,
          name: user?.fullName,
          createdAt: user?.createdAt,
        },
        exportDate: new Date().toISOString(),
        note: 'Dette er en simulert dataeksport. I produksjon ville dette inneholde alle dine prosjektdata, meldinger og innstillinger.'
      };
      
      const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `jobblogg-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Det oppstod en feil ved eksport av data. Prøv igjen senere.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    setIsDeleting(true);
    try {
      // This would call a Convex function to delete user account
      // For now, we'll just show a message
      alert('Kontosletting er ikke implementert i denne demoen. I produksjon ville dette permanent slettet alle dine data.');
    } catch (error) {
      console.error('Error deleting account:', error);
      alert('Det oppstod en feil ved sletting av konto. Kontakt support for hjelp.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <PageLayout 
      title="Personverninnstillinger" 
      showBackButton 
      backUrl="/"
      containerWidth="medium"
    >
      <div className="space-y-8">
        {/* User info */}
        <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-xl p-6">
          <h3 className="font-semibold text-jobblogg-primary mb-3">
            Din konto
          </h3>
          <div className="space-y-2 text-sm">
            <p><strong>E-post:</strong> {user?.primaryEmailAddress?.emailAddress}</p>
            <p><strong>Navn:</strong> {user?.fullName || 'Ikke oppgitt'}</p>
            <p><strong>Opprettet:</strong> {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('nb-NO') : 'Ukjent'}</p>
          </div>
        </div>

        {/* Cookie Settings */}
        <section>
          <CookieSettings />
        </section>

        {/* Data Rights */}
        <section className="space-y-6">
          <div>
            <Heading2>Dine datarettigheter</Heading2>
            <TextMuted className="mt-2">
              I henhold til GDPR har du følgende rettigheter over dine personopplysninger.
            </TextMuted>
          </div>

          <div className="grid gap-6 sm:grid-cols-2">
            {/* Data Export */}
            <div className="border border-jobblogg-border rounded-xl p-6">
              <div className="flex items-start gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-success/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                    Eksporter dine data
                  </h3>
                  <TextMuted className="text-sm mb-4">
                    Last ned alle dine personopplysninger i et strukturert format (JSON).
                  </TextMuted>
                  <PrimaryButton 
                    onClick={handleExportData}
                    disabled={isExporting}
                    className="w-full"
                  >
                    {isExporting ? 'Eksporterer...' : 'Last ned mine data'}
                  </PrimaryButton>
                </div>
              </div>
            </div>

            {/* Data Correction */}
            <div className="border border-jobblogg-border rounded-xl p-6">
              <div className="flex items-start gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                    Rett dine opplysninger
                  </h3>
                  <TextMuted className="text-sm mb-4">
                    Oppdater eller korriger dine personopplysninger i profilen din.
                  </TextMuted>
                  <SecondaryButton 
                    onClick={() => window.open('/user-profile', '_blank')}
                    className="w-full"
                  >
                    Gå til profil
                  </SecondaryButton>
                </div>
              </div>
            </div>

            {/* Data Restriction */}
            <div className="border border-jobblogg-border rounded-xl p-6">
              <div className="flex items-start gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-warning/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                    Begrens behandling
                  </h3>
                  <TextMuted className="text-sm mb-4">
                    Be om at vi begrenser behandlingen av dine personopplysninger.
                  </TextMuted>
                  <SecondaryButton 
                    onClick={() => window.open('mailto:<EMAIL>?subject=Begrens behandling av mine data')}
                    className="w-full"
                  >
                    Kontakt oss
                  </SecondaryButton>
                </div>
              </div>
            </div>

            {/* Data Objection */}
            <div className="border border-jobblogg-border rounded-xl p-6">
              <div className="flex items-start gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-error/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                    Motsett deg behandling
                  </h3>
                  <TextMuted className="text-sm mb-4">
                    Motsett deg behandling basert på berettiget interesse.
                  </TextMuted>
                  <SecondaryButton 
                    onClick={() => window.open('mailto:<EMAIL>?subject=Innsigelse mot databehandling')}
                    className="w-full"
                  >
                    Send innsigelse
                  </SecondaryButton>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Danger Zone */}
        <section className="space-y-4">
          <div>
            <Heading2 className="text-jobblogg-error">Farlig sone</Heading2>
            <TextMuted className="mt-2">
              Disse handlingene kan ikke angres. Vær sikker før du fortsetter.
            </TextMuted>
          </div>

          <div className="bg-jobblogg-error/5 border border-jobblogg-error/20 rounded-xl p-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-jobblogg-error rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-jobblogg-error mb-2">
                  Slett kontoen min
                </h3>
                <TextMuted className="text-sm mb-4">
                  Dette vil permanent slette kontoen din og alle tilknyttede data. 
                  Denne handlingen kan ikke angres.
                </TextMuted>
                
                {!showDeleteConfirm ? (
                  <SecondaryButton 
                    onClick={handleDeleteAccount}
                    className="border-jobblogg-error text-jobblogg-error hover:bg-jobblogg-error hover:text-white"
                  >
                    Slett konto
                  </SecondaryButton>
                ) : (
                  <div className="space-y-3">
                    <div className="bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg p-3">
                      <TextMuted className="text-sm font-medium text-jobblogg-error">
                        ⚠️ Er du sikker? Denne handlingen kan ikke angres.
                      </TextMuted>
                    </div>
                    <div className="flex gap-3">
                      <PrimaryButton 
                        onClick={handleDeleteAccount}
                        disabled={isDeleting}
                        className="bg-jobblogg-error hover:bg-jobblogg-error-dark"
                      >
                        {isDeleting ? 'Sletter...' : 'Ja, slett kontoen min'}
                      </PrimaryButton>
                      <SecondaryButton 
                        onClick={() => setShowDeleteConfirm(false)}
                        disabled={isDeleting}
                      >
                        Avbryt
                      </SecondaryButton>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Contact */}
        <section className="space-y-4">
          <Heading2>Trenger du hjelp?</Heading2>
          
          <div className="bg-jobblogg-neutral-secondary rounded-xl p-6">
            <TextMedium className="mb-4">
              Har du spørsmål om dine personvernrettigheter eller hvordan vi behandler dine data?
            </TextMedium>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <SecondaryButton 
                onClick={() => window.open('mailto:<EMAIL>')}
                className="flex-1"
              >
                Kontakt personvernansvarlig
              </SecondaryButton>
              <SecondaryButton 
                onClick={() => window.open('https://www.datatilsynet.no', '_blank')}
                className="flex-1"
              >
                Klage til Datatilsynet
              </SecondaryButton>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default PrivacySettings;
