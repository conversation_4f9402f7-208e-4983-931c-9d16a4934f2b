import { useState, useEffect, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';
import { offlineStorage, OfflineProject } from '../utils/offlineStorage';
import { offlineImageStorage, OfflineImage, ImageUploadQueueItem } from '../utils/offlineImageStorage';
import { usePWA } from './usePWA';

/**
 * Hook for managing projects with offline support
 * Combines online projects from Convex with offline projects from local storage
 */
export function useOfflineProjects() {
  const { user } = useUser();
  const { isOnline, canAccessOfflineData } = usePWA();
  const [offlineProjects, setOfflineProjects] = useState<OfflineProject[]>([]);
  const [isOfflineInitialized, setIsOfflineInitialized] = useState(false);
  const [offlineError, setOfflineError] = useState<string | null>(null);

  // Query online projects
  const onlineProjects = useQuery(
    api.projects.getByUserWithCustomers,
    user?.id ? { userId: user.id } : "skip"
  );

  // Initialize offline storage when user is authenticated and has consent
  useEffect(() => {
    const initializeOfflineStorage = async () => {
      if (!user?.id || !canAccessOfflineData) {
        setIsOfflineInitialized(false);
        setOfflineProjects([]);
        return;
      }

      try {
        const initialized = await offlineStorage.initializeForUser(user.id);
        setIsOfflineInitialized(initialized);
        
        if (initialized) {
          const projects = await offlineStorage.getAllProjects();
          setOfflineProjects(projects);
          setOfflineError(null);
        }
      } catch (error) {
        console.error('[useOfflineProjects] Failed to initialize offline storage:', error);
        setOfflineError('Kunne ikke initialisere offline-lagring');
        setIsOfflineInitialized(false);
      }
    };

    initializeOfflineStorage();
  }, [user?.id, canAccessOfflineData]);

  // Refresh offline projects periodically
  useEffect(() => {
    if (!isOfflineInitialized) return;

    const refreshOfflineProjects = async () => {
      try {
        const projects = await offlineStorage.getAllProjects();
        setOfflineProjects(projects);
      } catch (error) {
        console.error('[useOfflineProjects] Failed to refresh offline projects:', error);
      }
    };

    const interval = setInterval(refreshOfflineProjects, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [isOfflineInitialized]);

  // Combine online and offline projects
  const allProjects = useMemo(() => {
    const combined = [];
    
    // Add online projects
    if (onlineProjects) {
      combined.push(...onlineProjects.map(project => ({
        ...project,
        isOffline: false,
        syncStatus: 'synced' as const
      })));
    }

    // Add offline projects (avoid duplicates)
    if (offlineProjects.length > 0) {
      const onlineProjectIds = new Set(onlineProjects?.map(p => p._id) || []);
      const uniqueOfflineProjects = offlineProjects.filter(
        project => !onlineProjectIds.has(project.id)
      );
      combined.push(...uniqueOfflineProjects);
    }

    // Sort by creation date (newest first)
    return combined.sort((a, b) => {
      const dateA = new Date(a.createdAt || a._creationTime || 0);
      const dateB = new Date(b.createdAt || b._creationTime || 0);
      return dateB.getTime() - dateA.getTime();
    });
  }, [onlineProjects, offlineProjects]);

  // Create project offline
  const createProjectOffline = async (projectData: {
    title: string;
    description: string;
    status?: string;
  }): Promise<string> => {
    if (!user?.id || !isOfflineInitialized) {
      throw new Error('Offline storage ikke tilgjengelig');
    }

    const offlineId = `offline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const project = {
      id: offlineId,
      title: projectData.title,
      description: projectData.description,
      status: projectData.status || 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    try {
      await offlineStorage.addOfflineProject(project);
      
      // Refresh offline projects
      const updatedProjects = await offlineStorage.getAllProjects();
      setOfflineProjects(updatedProjects);
      
      return offlineId;
    } catch (error) {
      console.error('[useOfflineProjects] Failed to create offline project:', error);
      throw new Error('Kunne ikke opprette prosjekt offline');
    }
  };

  // Get project by ID (online or offline)
  const getProjectById = (projectId: string) => {
    return allProjects.find(project => 
      project._id === projectId || project.id === projectId
    );
  };

  // Check if project is offline
  const isProjectOffline = (projectId: string): boolean => {
    const project = getProjectById(projectId);
    return project?.isOffline === true;
  };

  // Get sync status for project
  const getProjectSyncStatus = (projectId: string): string => {
    const project = getProjectById(projectId);
    if (!project) return 'unknown';
    
    if (project.isOffline) {
      return project.syncStatus || 'pending';
    }
    
    return 'synced';
  };

  return {
    // Data
    projects: allProjects,
    onlineProjects: onlineProjects || [],
    offlineProjects,
    
    // State
    isOnline,
    isOfflineInitialized,
    canAccessOfflineData,
    offlineError,
    
    // Actions
    createProjectOffline,
    getProjectById,
    isProjectOffline,
    getProjectSyncStatus,
    
    // Utilities
    hasOfflineProjects: offlineProjects.length > 0,
    totalProjects: allProjects.length,
    onlineProjectCount: onlineProjects?.length || 0,
    offlineProjectCount: offlineProjects.length
  };
}

/**
 * Hook for managing project logs with offline support
 */
export function useOfflineProjectLogs(projectId: string) {
  const { user } = useUser();
  const { isOnline, canAccessOfflineData } = usePWA();
  const [offlineLogs, setOfflineLogs] = useState<any[]>([]);
  const [isOfflineInitialized, setIsOfflineInitialized] = useState(false);

  // Query online project logs
  const onlineLogs = useQuery(
    api.projectLogs.getProjectLogs,
    projectId && user?.id ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Initialize and load offline logs
  useEffect(() => {
    const loadOfflineLogs = async () => {
      if (!user?.id || !canAccessOfflineData || !projectId) {
        setOfflineLogs([]);
        return;
      }

      try {
        const initialized = await offlineStorage.initializeForUser(user.id);
        setIsOfflineInitialized(initialized);
        
        if (initialized) {
          const logs = await offlineStorage.getProjectLogs(projectId);
          setOfflineLogs(logs);
        }
      } catch (error) {
        console.error('[useOfflineProjectLogs] Failed to load offline logs:', error);
      }
    };

    loadOfflineLogs();
  }, [user?.id, canAccessOfflineData, projectId]);

  // Combine online and offline logs
  const allLogs = useMemo(() => {
    const combined = [];
    
    // Add online logs
    if (onlineLogs) {
      combined.push(...onlineLogs.map(log => ({
        ...log,
        isOffline: false,
        syncStatus: 'synced' as const
      })));
    }

    // Add offline logs
    if (offlineLogs.length > 0) {
      combined.push(...offlineLogs);
    }

    // Sort by creation date (newest first)
    return combined.sort((a, b) => {
      const dateA = new Date(a.createdAt || a._creationTime || 0);
      const dateB = new Date(b.createdAt || b._creationTime || 0);
      return dateB.getTime() - dateA.getTime();
    });
  }, [onlineLogs, offlineLogs]);

  // Create log entry offline
  const createLogOffline = async (logData: {
    description: string;
    images?: string[];
  }): Promise<string> => {
    if (!user?.id || !isOfflineInitialized || !projectId) {
      throw new Error('Offline storage ikke tilgjengelig');
    }

    const offlineId = `offline-log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const log = {
      id: offlineId,
      projectId,
      description: logData.description,
      images: logData.images || [],
      createdAt: new Date().toISOString()
    };

    try {
      await offlineStorage.addOfflineProjectLog(log);
      
      // Refresh offline logs
      const updatedLogs = await offlineStorage.getProjectLogs(projectId);
      setOfflineLogs(updatedLogs);
      
      return offlineId;
    } catch (error) {
      console.error('[useOfflineProjectLogs] Failed to create offline log:', error);
      throw new Error('Kunne ikke opprette loggoppføring offline');
    }
  };

  return {
    // Data
    logs: allLogs,
    onlineLogs: onlineLogs || [],
    offlineLogs,
    
    // State
    isOnline,
    isOfflineInitialized,
    canAccessOfflineData,
    
    // Actions
    createLogOffline,
    
    // Utilities
    hasOfflineLogs: offlineLogs.length > 0,
    totalLogs: allLogs.length,
    onlineLogCount: onlineLogs?.length || 0,
    offlineLogCount: offlineLogs.length
  };
}

/**
 * Hook for managing offline images
 */
export function useOfflineImages(projectId?: string) {
  const { user } = useUser();
  const { canAccessOfflineData } = usePWA();
  const [offlineImages, setOfflineImages] = useState<OfflineImage[]>([]);
  const [uploadQueue, setUploadQueue] = useState<ImageUploadQueueItem[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize offline image storage
  useEffect(() => {
    const initializeImageStorage = async () => {
      if (!user?.id || !canAccessOfflineData) {
        setIsInitialized(false);
        return;
      }

      try {
        const initialized = await offlineImageStorage.initializeForUser(user.id);
        setIsInitialized(initialized);
        setError(null);
      } catch (error) {
        console.error('[useOfflineImages] Failed to initialize:', error);
        setError('Kunne ikke initialisere offline bildelagring');
        setIsInitialized(false);
      }
    };

    initializeImageStorage();
  }, [user?.id, canAccessOfflineData]);

  // Load offline images for project
  useEffect(() => {
    const loadProjectImages = async () => {
      if (!isInitialized || !projectId) {
        setOfflineImages([]);
        return;
      }

      try {
        const images = await offlineImageStorage.getProjectImages(projectId);
        setOfflineImages(images);
      } catch (error) {
        console.error('[useOfflineImages] Failed to load project images:', error);
      }
    };

    loadProjectImages();
  }, [isInitialized, projectId]);

  // Load upload queue
  useEffect(() => {
    const loadUploadQueue = async () => {
      if (!isInitialized) {
        setUploadQueue([]);
        return;
      }

      try {
        const queue = await offlineImageStorage.getUploadQueue();
        setUploadQueue(queue);
      } catch (error) {
        console.error('[useOfflineImages] Failed to load upload queue:', error);
      }
    };

    loadUploadQueue();

    // Refresh queue periodically
    const interval = setInterval(loadUploadQueue, 30000);
    return () => clearInterval(interval);
  }, [isInitialized]);

  // Store image offline
  const storeImageOffline = async (
    file: File,
    targetProjectId?: string,
    logId?: string
  ): Promise<string> => {
    if (!isInitialized || !user?.id) {
      throw new Error('Offline bildelagring ikke tilgjengelig');
    }

    const useProjectId = targetProjectId || projectId;
    if (!useProjectId) {
      throw new Error('Prosjekt-ID mangler');
    }

    try {
      const imageId = await offlineImageStorage.storeImageOffline(useProjectId, file, logId);

      // Refresh images and queue
      const [images, queue] = await Promise.all([
        offlineImageStorage.getProjectImages(useProjectId),
        offlineImageStorage.getUploadQueue()
      ]);

      setOfflineImages(images);
      setUploadQueue(queue);

      return imageId;
    } catch (error) {
      console.error('[useOfflineImages] Failed to store image offline:', error);
      throw error;
    }
  };

  // Get offline image as blob
  const getOfflineImageBlob = async (imageId: string): Promise<Blob | null> => {
    if (!isInitialized) return null;
    return await offlineImageStorage.getOfflineImage(imageId);
  };

  // Get offline image thumbnail
  const getOfflineImageThumbnail = async (imageId: string): Promise<Blob | null> => {
    if (!isInitialized) return null;
    return await offlineImageStorage.getOfflineImageThumbnail(imageId);
  };

  // Get image URL (creates object URL for offline images)
  const getImageUrl = async (imageId: string): Promise<string | null> => {
    const blob = await getOfflineImageBlob(imageId);
    if (!blob) return null;
    return URL.createObjectURL(blob);
  };

  // Get thumbnail URL
  const getThumbnailUrl = async (imageId: string): Promise<string | null> => {
    const blob = await getOfflineImageThumbnail(imageId);
    if (!blob) return null;
    return URL.createObjectURL(blob);
  };

  return {
    // Data
    offlineImages,
    uploadQueue,

    // State
    isInitialized,
    canAccessOfflineData,
    error,

    // Actions
    storeImageOffline,
    getOfflineImageBlob,
    getOfflineImageThumbnail,
    getImageUrl,
    getThumbnailUrl,

    // Utilities
    hasOfflineImages: offlineImages.length > 0,
    hasUploadQueue: uploadQueue.length > 0,
    pendingUploads: uploadQueue.filter(item => item.imageData.syncStatus === 'pending').length,
    failedUploads: uploadQueue.filter(item => item.imageData.syncStatus === 'error').length
  };
}
