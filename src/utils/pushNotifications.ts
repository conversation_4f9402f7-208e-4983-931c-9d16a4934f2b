/**
 * Push Notification Utility for JobbLogg
 * Handles push notification setup, subscription, and testing
 */

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  image?: string;
  url?: string;
  projectId?: string;
  type?: 'chat' | 'project-update' | 'team-invite' | 'general';
  requireInteraction?: boolean;
  tag?: string;
}

/**
 * Push Notification Manager
 */
export class PushNotificationManager {
  private static instance: PushNotificationManager;
  private registration: ServiceWorkerRegistration | null = null;
  private subscription: PushSubscription | null = null;

  private constructor() {}

  static getInstance(): PushNotificationManager {
    if (!PushNotificationManager.instance) {
      PushNotificationManager.instance = new PushNotificationManager();
    }
    return PushNotificationManager.instance;
  }

  /**
   * Initialize push notifications
   */
  async initialize(): Promise<boolean> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.warn('[PushNotifications] Push notifications not supported');
      return false;
    }

    try {
      // Get service worker registration
      this.registration = await navigator.serviceWorker.ready;
      console.log('[PushNotifications] Service worker ready');

      // Check for existing subscription
      this.subscription = await this.registration.pushManager.getSubscription();
      if (this.subscription) {
        console.log('[PushNotifications] Existing subscription found');
      }

      return true;
    } catch (error) {
      console.error('[PushNotifications] Failed to initialize:', error);
      return false;
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribe(): Promise<PushSubscription | null> {
    if (!this.registration) {
      console.error('[PushNotifications] Service worker not registered');
      return null;
    }

    try {
      // Request notification permission if not granted
      if (Notification.permission !== 'granted') {
        const permission = await Notification.requestPermission();
        if (permission !== 'granted') {
          console.warn('[PushNotifications] Notification permission denied');
          return null;
        }
      }

      // Subscribe to push notifications
      this.subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          // You'll need to replace this with your actual VAPID public key
          process.env.VITE_VAPID_PUBLIC_KEY || 'YOUR_VAPID_PUBLIC_KEY_HERE'
        )
      });

      console.log('[PushNotifications] Successfully subscribed:', this.subscription);

      // TODO: Send subscription to your backend
      // await this.sendSubscriptionToServer(this.subscription);

      return this.subscription;
    } catch (error) {
      console.error('[PushNotifications] Failed to subscribe:', error);
      return null;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<boolean> {
    if (!this.subscription) {
      console.warn('[PushNotifications] No active subscription to unsubscribe');
      return true;
    }

    try {
      const success = await this.subscription.unsubscribe();
      if (success) {
        this.subscription = null;
        console.log('[PushNotifications] Successfully unsubscribed');
        
        // TODO: Remove subscription from your backend
        // await this.removeSubscriptionFromServer();
      }
      return success;
    } catch (error) {
      console.error('[PushNotifications] Failed to unsubscribe:', error);
      return false;
    }
  }

  /**
   * Check if user is subscribed
   */
  isSubscribed(): boolean {
    return this.subscription !== null;
  }

  /**
   * Get current subscription
   */
  getSubscription(): PushSubscription | null {
    return this.subscription;
  }

  /**
   * Test local notification (for development)
   */
  async testLocalNotification(payload: NotificationPayload): Promise<void> {
    if (!this.registration) {
      console.error('[PushNotifications] Service worker not registered');
      return;
    }

    if (Notification.permission !== 'granted') {
      console.warn('[PushNotifications] Notification permission not granted');
      return;
    }

    try {
      await this.registration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/icons/icon-192x192.svg',
        badge: '/icons/icon-192x192.svg',
        image: payload.image,
        data: {
          url: payload.url || '/',
          projectId: payload.projectId,
          type: payload.type || 'general'
        },
        actions: [
          {
            action: 'view',
            title: 'Vis',
            icon: '/icons/icon-192x192.svg'
          },
          {
            action: 'dismiss',
            title: 'Lukk'
          }
        ],
        tag: payload.tag || 'jobblogg-test',
        requireInteraction: payload.requireInteraction || false,
        vibrate: [200, 100, 200]
      });

      console.log('[PushNotifications] Test notification sent');
    } catch (error) {
      console.error('[PushNotifications] Failed to send test notification:', error);
    }
  }

  /**
   * Send subscription to server (placeholder)
   */
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    // TODO: Implement server endpoint to store subscription
    console.log('[PushNotifications] Would send subscription to server:', subscription);
    
    // Example implementation:
    /*
    try {
      await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription,
          userId: getCurrentUserId(), // You'll need to implement this
        }),
      });
    } catch (error) {
      console.error('Failed to send subscription to server:', error);
    }
    */
  }

  /**
   * Convert VAPID key to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }
}

/**
 * Notification Templates for JobbLogg
 */
export const NotificationTemplates = {
  chatMessage: (senderName: string, projectName: string, projectId: string): NotificationPayload => ({
    title: `💬 Ny melding fra ${senderName}`,
    body: `I prosjekt: ${projectName}`,
    type: 'chat',
    projectId,
    url: `/project/${projectId}#chat`,
    tag: `chat-${projectId}`,
    requireInteraction: true
  }),

  projectUpdate: (projectName: string, updateType: string, projectId: string): NotificationPayload => ({
    title: `📋 ${projectName}`,
    body: `${updateType}`,
    type: 'project-update',
    projectId,
    url: `/project/${projectId}`,
    tag: `project-${projectId}`
  }),

  teamInvite: (inviterName: string, projectName: string): NotificationPayload => ({
    title: `👥 Ny teaminvitasjon`,
    body: `${inviterName} har invitert deg til ${projectName}`,
    type: 'team-invite',
    url: '/dashboard#notifications',
    tag: 'team-invite',
    requireInteraction: true
  }),

  imageUploaded: (uploaderName: string, projectName: string, projectId: string): NotificationPayload => ({
    title: `📸 Nye bilder`,
    body: `${uploaderName} lastet opp bilder i ${projectName}`,
    type: 'project-update',
    projectId,
    url: `/project/${projectId}#images`,
    tag: `images-${projectId}`
  })
};

/**
 * Development Testing Utilities
 */
export const PushNotificationTesting = {
  /**
   * Test all notification types
   */
  async testAllNotifications(): Promise<void> {
    const pushManager = PushNotificationManager.getInstance();
    
    const testNotifications = [
      NotificationTemplates.chatMessage('Test Bruker', 'Test Prosjekt', 'test-123'),
      NotificationTemplates.projectUpdate('Test Prosjekt', 'Prosjektet er oppdatert', 'test-123'),
      NotificationTemplates.teamInvite('Test Bruker', 'Test Prosjekt'),
      NotificationTemplates.imageUploaded('Test Bruker', 'Test Prosjekt', 'test-123')
    ];

    for (let i = 0; i < testNotifications.length; i++) {
      setTimeout(() => {
        pushManager.testLocalNotification(testNotifications[i]);
      }, i * 2000); // 2 second delay between notifications
    }
  },

  /**
   * Test single notification
   */
  async testChatNotification(): Promise<void> {
    const pushManager = PushNotificationManager.getInstance();
    const notification = NotificationTemplates.chatMessage(
      'Robert Hansen', 
      'Kjøkkenrenovering Grünerløkka', 
      'proj-123'
    );
    
    await pushManager.testLocalNotification(notification);
  }
};

// Export singleton instance
export const pushNotificationManager = PushNotificationManager.getInstance();
