/**
 * GDPR-Compliant Offline Data Encryption Utility
 * Provides client-side encryption for offline data storage with user-specific keys
 */

// Types for encrypted data
export interface EncryptedData {
  data: string;
  iv: string;
  salt: string;
  timestamp: number;
}

export interface EncryptionKey {
  key: CryptoKey;
  salt: Uint8Array;
}

/**
 * Offline Data Encryption Manager
 * Uses Web Crypto API for secure client-side encryption
 */
export class OfflineEncryptionManager {
  private static instance: OfflineEncryptionManager;
  private encryptionKey: CryptoKey | null = null;
  private keyDerivationSalt: Uint8Array | null = null;

  private constructor() {}

  static getInstance(): OfflineEncryptionManager {
    if (!OfflineEncryptionManager.instance) {
      OfflineEncryptionManager.instance = new OfflineEncryptionManager();
    }
    return OfflineEncryptionManager.instance;
  }

  /**
   * Initialize encryption with user-specific key derivation
   * Uses user ID and session data to create unique encryption keys
   */
  async initializeEncryption(userId: string, sessionToken?: string): Promise<void> {
    try {
      // Create user-specific salt
      const userSalt = new TextEncoder().encode(userId + (sessionToken || ''));
      const saltBuffer = await crypto.subtle.digest('SHA-256', userSalt);
      this.keyDerivationSalt = new Uint8Array(saltBuffer.slice(0, 16));

      // Derive encryption key from user data
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(userId + '-jobblogg-offline'),
        { name: 'PBKDF2' },
        false,
        ['deriveKey']
      );

      this.encryptionKey = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: this.keyDerivationSalt,
          iterations: 100000,
          hash: 'SHA-256'
        },
        keyMaterial,
        { name: 'AES-GCM', length: 256 },
        false,
        ['encrypt', 'decrypt']
      );

      console.log('[OfflineEncryption] Encryption initialized for user:', userId.substring(0, 8) + '...');
    } catch (error) {
      console.error('[OfflineEncryption] Failed to initialize encryption:', error);
      throw new Error('Kunne ikke initialisere kryptering for offline data');
    }
  }

  /**
   * Encrypt data for offline storage
   */
  async encryptData(data: any): Promise<EncryptedData> {
    if (!this.encryptionKey || !this.keyDerivationSalt) {
      throw new Error('Kryptering ikke initialisert. Kan ikke lagre data offline.');
    }

    try {
      // Convert data to JSON string
      const jsonData = JSON.stringify(data);
      const dataBuffer = new TextEncoder().encode(jsonData);

      // Generate random IV for this encryption
      const iv = crypto.getRandomValues(new Uint8Array(12));

      // Encrypt the data
      const encryptedBuffer = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        dataBuffer
      );

      return {
        data: this.arrayBufferToBase64(encryptedBuffer),
        iv: this.arrayBufferToBase64(iv),
        salt: this.arrayBufferToBase64(this.keyDerivationSalt),
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[OfflineEncryption] Encryption failed:', error);
      throw new Error('Kunne ikke kryptere data for offline lagring');
    }
  }

  /**
   * Decrypt data from offline storage
   */
  async decryptData<T>(encryptedData: EncryptedData): Promise<T> {
    if (!this.encryptionKey) {
      throw new Error('Kryptering ikke initialisert. Kan ikke lese offline data.');
    }

    try {
      // Convert base64 back to buffers
      const dataBuffer = this.base64ToArrayBuffer(encryptedData.data);
      const iv = this.base64ToArrayBuffer(encryptedData.iv);

      // Decrypt the data
      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        dataBuffer
      );

      // Convert back to JSON
      const jsonString = new TextDecoder().decode(decryptedBuffer);
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('[OfflineEncryption] Decryption failed:', error);
      throw new Error('Kunne ikke dekryptere offline data. Data kan være korrupt.');
    }
  }

  /**
   * Clear encryption keys (call on logout)
   */
  clearEncryption(): void {
    this.encryptionKey = null;
    this.keyDerivationSalt = null;
    console.log('[OfflineEncryption] Encryption keys cleared');
  }

  /**
   * Check if encryption is initialized
   */
  isInitialized(): boolean {
    return this.encryptionKey !== null && this.keyDerivationSalt !== null;
  }

  // Utility methods for base64 conversion
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }
}

/**
 * GDPR Compliance Manager for Offline Data
 */
export class GDPRComplianceManager {
  private static instance: GDPRComplianceManager;
  private consentTimestamp: number | null = null;

  private constructor() {}

  static getInstance(): GDPRComplianceManager {
    if (!GDPRComplianceManager.instance) {
      GDPRComplianceManager.instance = new GDPRComplianceManager();
    }
    return GDPRComplianceManager.instance;
  }

  /**
   * Record user consent for offline data storage
   */
  recordConsent(userId: string): void {
    const consentData = {
      userId,
      timestamp: Date.now(),
      version: '1.0',
      features: ['offline-storage', 'data-encryption', 'local-caching']
    };

    localStorage.setItem('jobblogg-gdpr-consent', JSON.stringify(consentData));
    this.consentTimestamp = Date.now();
    
    console.log('[GDPR] Offline data consent recorded for user:', userId.substring(0, 8) + '...');
  }

  /**
   * Check if user has given consent for offline data storage
   */
  hasValidConsent(userId: string): boolean {
    try {
      const consentData = localStorage.getItem('jobblogg-gdpr-consent');
      if (!consentData) return false;

      const consent = JSON.parse(consentData);
      
      // Check if consent is for current user and not expired (1 year)
      const isValidUser = consent.userId === userId;
      const isNotExpired = (Date.now() - consent.timestamp) < (365 * 24 * 60 * 60 * 1000);
      
      return isValidUser && isNotExpired;
    } catch (error) {
      console.error('[GDPR] Error checking consent:', error);
      return false;
    }
  }

  /**
   * Revoke consent and clear all offline data
   */
  revokeConsent(): void {
    // Clear consent record
    localStorage.removeItem('jobblogg-gdpr-consent');
    
    // Clear all offline data
    this.clearAllOfflineData();
    
    // Clear encryption keys
    OfflineEncryptionManager.getInstance().clearEncryption();
    
    console.log('[GDPR] Consent revoked and offline data cleared');
  }

  /**
   * Clear all offline data (GDPR right to erasure)
   */
  private clearAllOfflineData(): void {
    const offlineKeys = [
      'jobblogg-offline-data',
      'jobblogg-sync-queue',
      'jobblogg-last-sync',
      'jobblogg-offline-images',
      'jobblogg-offline-enabled'
    ];

    offlineKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear IndexedDB offline data if exists
    if ('indexedDB' in window) {
      indexedDB.deleteDatabase('jobblogg-offline-db');
    }
  }

  /**
   * Get data processing information for transparency
   */
  getDataProcessingInfo(): {
    purpose: string;
    dataTypes: string[];
    retention: string;
    security: string[];
  } {
    return {
      purpose: 'Muliggjøre offline bruk av JobbLogg-appen for prosjektdokumentasjon',
      dataTypes: [
        'Prosjektinformasjon (navn, beskrivelse, status)',
        'Prosjektlogger (beskrivelser, bilder)',
        'Brukerpreferanser for offline-funksjonalitet'
      ],
      retention: 'Data lagres lokalt til du logger ut eller trekker tilbake samtykke',
      security: [
        'AES-256 kryptering av alle offline data',
        'Brukerspecifikke krypteringsnøkler',
        'Automatisk sletting ved utlogging',
        'Ingen deling med tredjeparter'
      ]
    };
  }
}

// Export singleton instances
export const offlineEncryption = OfflineEncryptionManager.getInstance();
export const gdprCompliance = GDPRComplianceManager.getInstance();
