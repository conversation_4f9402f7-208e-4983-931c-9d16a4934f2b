{"name": "jobblogg", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node scripts/dev-start.js", "dev:clear": "node scripts/dev-start.js --clear-ports", "dev:vite": "node scripts/dev-start.js --vite-only", "dev:convex": "node scripts/dev-start.js --convex-only", "dev:vite:force": "npm run port-clear && vite", "dev:low-memory": "node --max-old-space-size=2048 scripts/dev-start.js", "build": "npm run validate:pre-build && tsc -b && vite build", "lint": "eslint .", "lint:jobblogg": "eslint . --config .eslintrc.jobblogg.js", "preview": "npm run port-check && vite preview", "preview:force": "npm run port-clear && vite preview", "port-check": "node scripts/port-manager.js", "port-clear": "node scripts/port-manager.js --kill", "validate:imports": "node scripts/validate-imports.js", "validate:pre-build": "npm run validate:imports && npm run type-check", "validate:pre-commit": "npm run lint:jobblogg && npm run validate:imports && npm run type-check", "type-check": "tsc --noEmit", "generate:component": "node scripts/component-template-generator.js", "test:imports": "npm run validate:imports && echo '✅ Import validation passed'", "test:dynamic-imports": "npm run build && echo '✅ Dynamic imports validated in build'"}, "dependencies": {"@clerk/clerk-react": "^5.32.1", "@clerk/localizations": "^3.19.0", "@types/react-router-dom": "^5.3.3", "convex": "^1.25.0", "nanoid": "^5.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.6.2", "resend": "^4.7.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}