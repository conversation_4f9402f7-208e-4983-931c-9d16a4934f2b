// JobbLogg Service Worker
// Version 1.0.0 - Mobile-First PWA with Push Notifications

const CACHE_NAME = 'jobblogg-v1.0.0';
const STATIC_CACHE = 'jobblogg-static-v1.0.0';
const DYNAMIC_CACHE = 'jobblogg-dynamic-v1.0.0';
const IMAGE_CACHE = 'jobblogg-images-v1.0.0';

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/icons/icon-192x192.svg',
  '/icons/icon-512x512.svg',
  '/vite.svg',
  // Add other critical static assets
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /^https:\/\/.*\.convex\.cloud\/api\//,
  /^https:\/\/.*\.clerk\.accounts\.dev\//
];

// Image patterns to cache
const IMAGE_PATTERNS = [
  /\.(jpg|jpeg|png|gif|webp|svg)$/i
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== IMAGE_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle different types of requests with appropriate strategies
  if (isStaticAsset(request)) {
    // Static assets: Cache First
    event.respondWith(cacheFirst(request, STATIC_CACHE));
  } else if (isImageRequest(request)) {
    // Images: Cache First with fallback
    event.respondWith(cacheFirstWithFallback(request, IMAGE_CACHE));
  } else if (isAPIRequest(request)) {
    // API requests: Network First with cache fallback
    event.respondWith(networkFirstWithCache(request, DYNAMIC_CACHE));
  } else if (isNavigationRequest(request)) {
    // Navigation: Network First with offline fallback
    event.respondWith(navigationHandler(request));
  } else {
    // Other requests: Network First
    event.respondWith(networkFirst(request, DYNAMIC_CACHE));
  }
});

// Caching Strategies

// Cache First - for static assets
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('[SW] Cache First failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

// Cache First with Fallback - for images
async function cacheFirstWithFallback(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      // Only cache successful responses
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('[SW] Image cache failed:', error);
    // Return a fallback image or placeholder
    return new Response('', { status: 404 });
  }
}

// Network First with Cache - for API requests
async function networkFirstWithCache(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', error);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response(JSON.stringify({ 
      error: 'Offline', 
      message: 'Ingen nettverkstilkobling tilgjengelig' 
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Network First - for general requests
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

// Navigation Handler - for page requests
async function navigationHandler(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    // Return cached index.html for offline navigation
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match('/index.html');
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

// Helper functions
function isStaticAsset(request) {
  return request.url.includes('/assets/') || 
         request.url.includes('/icons/') ||
         request.url.endsWith('.css') ||
         request.url.endsWith('.js') ||
         request.url.endsWith('.woff2');
}

function isImageRequest(request) {
  return IMAGE_PATTERNS.some(pattern => pattern.test(request.url));
}

function isAPIRequest(request) {
  return API_CACHE_PATTERNS.some(pattern => pattern.test(request.url));
}

function isNavigationRequest(request) {
  return request.mode === 'navigate';
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-projects') {
    event.waitUntil(syncProjects());
  }
});

// Sync offline project data
async function syncProjects() {
  try {
    // Implementation for syncing offline project data
    console.log('[SW] Syncing offline project data...');
    // This would integrate with your Convex backend
  } catch (error) {
    console.error('[SW] Sync failed:', error);
  }
}

// Push notifications (for future implementation)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: data.data,
      actions: [
        {
          action: 'view',
          title: 'Se prosjekt',
          icon: '/icons/action-view.png'
        },
        {
          action: 'dismiss',
          title: 'Lukk',
          icon: '/icons/action-dismiss.png'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Push notification handler
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received:', event);

  if (!event.data) {
    console.log('[SW] Push event but no data');
    return;
  }

  try {
    const data = event.data.json();
    console.log('[SW] Push data:', data);

    const options = {
      body: data.body || 'Ny aktivitet i JobbLogg',
      icon: '/icons/icon-192x192.svg',
      badge: '/icons/icon-192x192.svg',
      image: data.image,
      data: {
        url: data.url || '/',
        projectId: data.projectId,
        type: data.type
      },
      actions: [
        {
          action: 'view',
          title: 'Vis',
          icon: '/icons/icon-192x192.svg'
        },
        {
          action: 'dismiss',
          title: 'Lukk'
        }
      ],
      tag: data.tag || 'jobblogg-notification',
      requireInteraction: data.requireInteraction || false,
      silent: false,
      vibrate: [200, 100, 200]
    };

    event.waitUntil(
      self.registration.showNotification(data.title || 'JobbLogg', options)
    );
  } catch (error) {
    console.error('[SW] Error processing push notification:', error);

    // Fallback notification
    event.waitUntil(
      self.registration.showNotification('JobbLogg', {
        body: 'Du har en ny oppdatering',
        icon: '/icons/icon-192x192.svg',
        data: { url: '/' }
      })
    );
  }
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event);
  event.notification.close();

  const data = event.notification.data || {};
  let targetUrl = data.url || '/';

  // Handle different notification types
  if (data.type === 'chat' && data.projectId) {
    targetUrl = `/project/${data.projectId}#chat`;
  } else if (data.type === 'project-update' && data.projectId) {
    targetUrl = `/project/${data.projectId}`;
  } else if (data.type === 'team-invite') {
    targetUrl = '/dashboard#notifications';
  }

  if (event.action === 'view' || !event.action) {
    event.waitUntil(
      clients.matchAll({ type: 'window', includeUncontrolled: true })
        .then((clientList) => {
          // Check if JobbLogg is already open
          for (const client of clientList) {
            if (client.url.includes(self.location.origin)) {
              client.focus();
              client.postMessage({
                type: 'NOTIFICATION_CLICKED',
                url: targetUrl,
                data: data
              });
              return;
            }
          }

          // Open new window if not already open
          return clients.openWindow(targetUrl);
        })
    );
  }
});

// Notification close handler
self.addEventListener('notificationclose', (event) => {
  console.log('[SW] Notification closed:', event.notification.tag);

  // Track notification dismissal if needed
  // Could send analytics data here
});
