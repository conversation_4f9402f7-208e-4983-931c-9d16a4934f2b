#!/usr/bin/env node

/**
 * JobbLogg Development Server Starter
 * 
 * This script provides a controlled way to start the development environment
 * with consistent port management and proper error handling.
 */

import { spawn } from 'child_process';
import { promisify } from 'util';
import { exec } from 'child_process';

const execAsync = promisify(exec);

// Configuration
const CONFIG = {
  vite: {
    port: 5173,
    command: 'npx',
    args: ['vite'],
    name: 'Vite Dev Server',
    url: 'http://localhost:5173/'
  },
  convex: {
    command: 'npx',
    args: ['convex', 'dev'],
    name: 'Convex Backend',
    url: 'Convex Dashboard'
  }
};

let processes = {};
let isShuttingDown = false;
let restartAttempts = {};

/**
 * Graceful shutdown handler
 */
function setupGracefulShutdown() {
  const shutdown = (signal) => {
    if (isShuttingDown) return;
    isShuttingDown = true;
    
    console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
    
    Object.entries(processes).forEach(([name, process]) => {
      if (process && !process.killed) {
        console.log(`   Stopping ${name}...`);
        process.kill('SIGTERM');
        
        // Force kill after 5 seconds if not stopped
        setTimeout(() => {
          if (!process.killed) {
            console.log(`   Force killing ${name}...`);
            process.kill('SIGKILL');
          }
        }, 5000);
      }
    });
    
    setTimeout(() => {
      console.log('✅ Shutdown complete');
      process.exit(0);
    }, 1000);
  };
  
  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGQUIT', () => shutdown('SIGQUIT'));
}

/**
 * Start a service with proper logging
 */
function startService(serviceName, config) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Starting ${config.name}...`);

    const childProcess = spawn(config.command, config.args, {
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: process.cwd()
    });

    processes[serviceName] = childProcess;

    let hasStarted = false;
    let startupTimeout;

    // Handle stdout
    childProcess.stdout.on('data', (data) => {
      const output = data.toString();
      
      // Log with service prefix
      output.split('\n').forEach(line => {
        if (line.trim()) {
          console.log(`[${serviceName.toUpperCase()}] ${line}`);
        }
      });
      
      // Check for startup indicators
      if (!hasStarted) {
        if (
          (serviceName === 'vite' && output.includes('ready in')) ||
          (serviceName === 'convex' && output.includes('Convex functions ready'))
        ) {
          hasStarted = true;
          clearTimeout(startupTimeout);
          console.log(`✅ ${config.name} is ready!`);
          if (config.url) {
            console.log(`   ${config.url}`);
          }
          resolve(childProcess);
        }
      }
    });

    // Handle stderr
    childProcess.stderr.on('data', (data) => {
      const output = data.toString();
      
      // Check for port conflicts
      if (output.includes('EADDRINUSE') || output.includes('port') && output.includes('already in use')) {
        console.error(`❌ ${config.name} failed: Port conflict detected`);
        console.error('   Run "npm run port-clear" to clear conflicting processes');
        reject(new Error('Port conflict'));
        return;
      }
      
      // Log errors with service prefix
      output.split('\n').forEach(line => {
        if (line.trim()) {
          console.error(`[${serviceName.toUpperCase()}] ERROR: ${line}`);
        }
      });
    });

    // Handle process exit
    childProcess.on('exit', (code, signal) => {
      if (!isShuttingDown) {
        if (signal === 'SIGKILL') {
          console.error(`❌ ${config.name} was killed by system (SIGKILL) - likely due to memory pressure`);
          console.error('   💡 Try restarting with: npm run dev:clear');
          console.error('   💡 Or restart individual services: npm run dev:vite or npm run dev:convex');

          // Auto-restart Vite if it was killed (but not Convex, as it's more stable)
          if (serviceName === 'vite') {
            const attempts = restartAttempts[serviceName] || 0;
            if (attempts < 3) {
              restartAttempts[serviceName] = attempts + 1;
              console.log(`🔄 Auto-restarting ${config.name} (attempt ${attempts + 1}/3)...`);
              setTimeout(() => {
                startService(serviceName, config).catch(error => {
                  console.error(`❌ Auto-restart failed:`, error.message);
                });
              }, 2000);
            } else {
              console.error(`❌ Max restart attempts reached for ${config.name}`);
              console.error('   Please restart manually: npm run dev:clear');
            }
          }
        } else {
          console.error(`❌ ${config.name} exited unexpectedly (code: ${code}, signal: ${signal})`);
        }
      }
      delete processes[serviceName];
    });

    childProcess.on('error', (error) => {
      console.error(`❌ Failed to start ${config.name}:`, error.message);
      reject(error);
    });

    // Startup timeout
    startupTimeout = setTimeout(() => {
      if (!hasStarted) {
        console.log(`⏳ ${config.name} is taking longer than expected to start...`);
        resolve(childProcess); // Don't fail, just continue
      }
    }, 30000);
  });
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const shouldClearPorts = args.includes('--clear-ports') || args.includes('-c');
  const viteOnly = args.includes('--vite-only');
  const convexOnly = args.includes('--convex-only');
  
  console.log('🎯 JobbLogg Development Environment Starter\n');
  
  // Clear ports if requested
  if (shouldClearPorts) {
    console.log('🔄 Clearing ports...');
    try {
      await execAsync('node scripts/port-manager.js --kill');
    } catch (error) {
      console.error('❌ Failed to clear ports:', error.message);
      process.exit(1);
    }
  }
  
  // Check ports
  try {
    await execAsync('node scripts/port-manager.js');
  } catch (error) {
    console.error('❌ Port check failed. Use --clear-ports to automatically clear them.');
    process.exit(1);
  }
  
  setupGracefulShutdown();
  
  try {
    // Start services based on arguments
    if (!convexOnly) {
      await startService('vite', CONFIG.vite);
    }
    
    if (!viteOnly) {
      await startService('convex', CONFIG.convex);
    }
    
    console.log('\n🎉 Development environment is ready!');
    console.log('   Press Ctrl+C to stop all services\n');
    
    // Keep the process alive
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Failed to start development environment:', error.message);
    process.exit(1);
  }
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});

main();
